{"meta": {"instanceId": "gemini-seo-workflow-fixed"}, "nodes": [{"parameters": {"assignments": {"assignments": [{"id": "5e8d95c1-407c-4adc-b61a-a8ded2aed84a", "name": "formData", "type": "object", "value": "={{ $json }}"}, {"id": "31fe2c22-f140-4a89-8f98-af0eff940e82", "name": "projectId", "type": "string", "value": "={{ $now.valueOf() }}"}]}, "options": {}}, "id": "8082186e-d9ff-4dee-b8cd-274c1a8a70f6", "name": "Process Form Data", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [432, 0]}, {"parameters": {"promptType": "define", "text": "=Based on the Google HelpFul Content & the Checklist Outline & Article Methodology that you have been provided in the MCP_Checklist_Semantic_Content.\nI want you to create the outline for the content of the query \"{{ $('Process Form Data').item.json.formData.Query }}\".\n\nThe outline must include these required sections:[{{ $('Process Form Data').item.json.formData['outline focus'] }}]\n\n\nPlease provide a complete outline with detailed article methodology for each section.\n\n[final reminder]:\n\n1.  Output should be in {{ $('Process Form Data').item.json.formData['Output language'] }}\n2. The output should be in the markdown format to easy copy & pase to Google Doc.\n3. If the outline have a list, like 30+ checklist or 30+ benefits. Try to include the full 30+ checklist in the outline or the output of it.\n4. This is the year of 2025.\n5. The output only contain the outline & article methodology of blogpost/the content.", "hasOutputParser": true, "options": {"systemMessage": "=[role]:\nYou are a Semantic SEO Expert knowledgeable about topical authority, semantic content, and Creating great Semantic SEO-friendly Content.\n\n\n[Tools]:\n- Gemini_Search_Pro: Use this tool to Research the newest and accurate information that is needed in the Article methodology to create great semantic content that reflects the brand source context. (limit to 3 times)\n- Mcp_Checklist_Semantic_Content: Use this tool to retrieve the full requirements from the pre-loaded Google_HelpFul_Content and Checklist_Outline_and_Article_Methodology documents. **Call this tool without any input parameters.**\n- Think: Use the tool to think and ensure the final output will try its best to fulfill the search intent, the guidlines and the checklists from Mcp_checklist_semantic_content"}}, "id": "2845e040-cfbd-4c99-9d83-574fa541650f", "name": "Content Outline Generator", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [480, 368]}, {"parameters": {"assignments": {"assignments": [{"id": "e0a93d8d-4363-467e-9a13-e8e4df96dbb9", "name": "ContentOutline", "type": "string", "value": "={{ $json.output }}"}]}, "options": {}}, "id": "df78896c-7e51-4a1c-8a54-839ccd1f9467", "name": "Store Content Outline", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [864, 368]}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Create a slug for the following outline of the Blogpost/Content: \n[{{ $('Store Content Outline').item.json.ContentOutline }}]\n\nA slug in a blog post is the part of the URL that comes after the domain name and identifies a specific page. It is typically a short, descriptive phrase that summarizes the content of the post, making it easier for users and search engines to understand what the page is about. For example, in the URL www.example.com/intelligent-agents, the slug is intelligent-agents. A good slug is concise, contains relevant keywords, and avoids unnecessary words to improve readability and SEO. \n\nThe slug must be 4 or 5 words max and must include the primary keyword of the blog post which is {{ $('Process Form Data').item.json.formData.Query }}. Try it as short as possible.\n\nYour output must be the slug and nothing else so that I can copy and paste your output and put it at the end of my blog post URL to post it right away. "}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [192, 832], "id": "86817137-6ab3-46e4-8afe-d6f9fd347588", "name": "Slug"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-2024-11-20", "mode": "list", "cachedResultName": "GPT-4O-2024-11-20"}, "messages": {"values": [{"content": "=Extract the blog post title from the Blogpost/Content: \n[{{ $('Store Content Outline').item.json.ContentOutline }}]\n\n\nThe blog post title must include the primary keyword {{ $('Google Sheets1').item.json.Query }} and must inform the users right away of what they can expect from reading the blog post. \n\n- Don't put the output in \"\". The output should just text with no markdown or formatting. \n\nYour output must only be the blog post title and nothing else. "}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [560, 832], "id": "ada07f71-8d91-4bc0-8176-5b9d60525393", "name": "Title"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-2024-11-20", "mode": "list", "cachedResultName": "GPT-4O-2024-11-20"}, "messages": {"values": [{"content": "=Create a good meta description for the following blog post: \n\n[{{ $('Store Content Outline').item.json.ContentOutline }}]\n\nA good meta description for a blog post that is SEO-optimized should:\n- Be Concise: Stick to 150-160 characters to ensure the full description displays in search results. \n- Include Keywords: Incorporate semantic keywords that are relate to the main keyword which is [{{ $('Process Form Data').item.json.formData.Query }}] naturally to improve visibility and relevance to search queries \n- Provide Value: Clearly describe what the reader will learn or gain by clicking the link. \n\n- Be Engaging: Use persuasive language, such as action verbs or a question, to encourage clicks. \n\n- Align with Content: Ensure the description accurately reflects the blog post to meet user expectations and reduce bounce rates. \n\nYour output must only be the meta description and nothing else. \n"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [960, 832], "id": "c1f45426-4f78-464b-8357-ad52acca28a9", "name": "Meta description"}, {"parameters": {"rule": {"interval": [{"field": "hours"}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, 0], "id": "de354698-2def-465f-89ec-abbf0fd75386", "name": "Schedule Trigger1"}, {"parameters": {"documentId": {"__rl": true, "value": "1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM", "mode": "list", "cachedResultName": "Ekotek | Workflow Automation N8n - Outline & Keywords Update", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM/edit#gid=*********"}, "filtersUI": {"values": [{"lookupColumn": "Status", "lookupValue": "Run"}]}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [224, 0], "id": "a14ca7e1-a133-47b0-8e1a-9c72736e1b81", "name": "Google Sheets1", "alwaysOutputData": true, "credentials": {"googleSheetsOAuth2Api": {"id": "wQQPRkUeeGCPHnlK", "name": "Google Sheets account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "4fc7d4cb-b109-4202-b75e-57fe1715e33d", "name": "Search Intent analyze result", "value": "={{ $json.candidates[0].content.parts[0].text }}", "type": "string"}]}, "options": {"ignoreConversionErrors": true}}, "id": "74ed4f53-d078-4668-b886-7a4a10486567", "name": "Store Search Intent", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [864, 0]}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-goog-api-key", "value": "={{ $vars.GEMINI_API_KEY }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"[Knowledge about User's Search Intent]: {{ JSON.stringify($json['Search Intent analyze result']).slice(1, -1) }}\\n\\n[Instruction]: Answer in Markdown format with clear structure, bullet points, and short paragraphs.\\n\\n[Role]: You are a Semantic SEO Expert who is a master of the Topical Authority Concept from Koray. Your task is to analyze the competitor's content outline with the provided 'User's Search Intent' in mind.\\n\\n## Task:\\nIdentify the 10 best online articles that answer the query: {{ $('Process Form Data').item.json.formData.Query }} which means {{ $('Process Form Data').item.json.formData['Query in English'] }}, and satisfy the search intent that you have been provided\\n\\n### Requirements:\\n- Explain WHY these articles are the best.\\n- EXCLUDE any video content.\\n- Search and compare both {{ $('Process Form Data').item.json.formData['Output language'] }} & English articles, but prioritize {{ $('Process Form Data').item.json.formData['Output language'] }}.\\n\\n### Output Language:\\n{{ $('Process Form Data').item.json.formData['Output language'] }}\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 1,\n    \"maxOutputTokens\": 32000\n  },\n  \"tools\": [\n    {\n      \"googleSearchRetrieval\": {\n        \"dynamicRetrievalConfig\": {\n          \"mode\": \"MODE_DYNAMIC\",\n          \"dynamicThreshold\": 0.7\n        }\n      }\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1104, 0], "id": "f5abf022-8c09-4e1f-86f8-96d52ab56130", "name": "Gemini Get Top 10 Results"}, {"parameters": {"toolDescription": "Use this tool to Research the newest and accurate information that is needed in the Article methodology to create great semantic content that reflects the brand source context", "method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-goog-api-key", "value": "={{ $vars.GEMINI_API_KEY }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"You are a Semantic SEO Expert who is a master of the Topical Authority Concept from Koray. Your task is to analyze the competitor's content outline or a specific URL based on the provided query.\\n\\nQuery: {query}\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 0.8,\n    \"maxOutputTokens\": 20000\n  },\n  \"tools\": [\n    {\n      \"googleSearchRetrieval\": {\n        \"dynamicRetrievalConfig\": {\n          \"mode\": \"MODE_DYNAMIC\",\n          \"dynamicThreshold\": 0.7\n        }\n      }\n    }\n  ]\n}", "placeholderDefinitions": {"values": [{"name": "query", "description": "Performs search using Gemini with Google Search grounding based on the provided query, which can include text and URLs.", "type": "string"}]}}, "id": "6c2793f8-7acc-4ad8-9b82-21f97fb5d445", "name": "Gemini_Search_Pro", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [512, 624]}, {"parameters": {"sseEndpoint": "https://gtvseo.app.n8n.cloud/mcp/ad951871-27a9-495f-bc4d-3d4fab6465f9/sse", "options": {}}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1, "position": [640, 624], "id": "8c91188b-9586-49c8-a637-8e6836803638", "name": "Mcp_Checklist_Semantic_Content"}, {"parameters": {"assignments": {"assignments": [{"id": "40e327aa-3eaf-4cd7-b59e-8273b3c05355", "name": "Top 10 Analysis Result", "value": "={{ $json.candidates[0].content.parts[0].text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1312, 0], "id": "e982bbc9-f5aa-45db-a84d-bd9f7e56cec6", "name": "Store Top 10 Analysis"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-goog-api-key", "value": "={{ $vars.GEMINI_API_KEY }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"[Instruction]: Answer in Markdown format with clear structure, bullet points, and short paragraphs.\\n\\n[Role]: You are a Researcher & Semantic SEO Expert who is a master of the Topical Authority Concept from Koray. Your task is to analyze the Search Intent behind the query.\\n\\nAnalyze the query: {{ $json.formData.Query }} which means {{ $json.formData['Query in English'] }}; what would they be looking for if they searched it? What is the search intent on Google and how to fulfill it? Search and compare both {{ $('Process Form Data').item.json.formData['Output language'] }} & English articles, but prioritize {{ $('Process Form Data').item.json.formData['Output language'] }}.\\n\\n### Output Language:\\n{{ $('Process Form Data').item.json.formData['Output language'] }}\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 1,\n    \"maxOutputTokens\": 10000\n  },\n  \"tools\": [\n    {\n      \"googleSearchRetrieval\": {\n        \"dynamicRetrievalConfig\": {\n          \"mode\": \"MODE_DYNAMIC\",\n          \"dynamicThreshold\": 0.7\n        }\n      }\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [640, 0], "id": "d6d28b23-3a33-4015-ae3f-d18bbc360524", "name": "Gemini Search Intent Analyze"}], "connections": {"Process Form Data": {"main": [[{"node": "Gemini Search Intent Analyze", "type": "main", "index": 0}]]}, "Content Outline Generator": {"main": [[{"node": "Store Content Outline", "type": "main", "index": 0}]]}, "Store Content Outline": {"main": [[{"node": "Slug", "type": "main", "index": 0}]]}, "Slug": {"main": [[{"node": "Title", "type": "main", "index": 0}]]}, "Title": {"main": [[{"node": "Meta description", "type": "main", "index": 0}]]}, "Schedule Trigger1": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}]]}, "Google Sheets1": {"main": [[{"node": "Process Form Data", "type": "main", "index": 0}]]}, "Store Search Intent": {"main": [[{"node": "Gemini Get Top 10 Results", "type": "main", "index": 0}]]}, "Gemini Get Top 10 Results": {"main": [[{"node": "Store Top 10 Analysis", "type": "main", "index": 0}]]}, "Gemini_Search_Pro": {"ai_tool": [[{"node": "Content Outline Generator", "type": "ai_tool", "index": 0}]]}, "Mcp_Checklist_Semantic_Content": {"ai_tool": [[{"node": "Content Outline Generator", "type": "ai_tool", "index": 0}]]}, "Store Top 10 Analysis": {"main": [[{"node": "Content Outline Generator", "type": "main", "index": 0}]]}, "Gemini Search Intent Analyze": {"main": [[{"node": "Store Search Intent", "type": "main", "index": 0}]]}}}