# n8n Workflow Import Guide - Gemini SEO Workflows

## Issue Resolution

The original workflow files had JSON formatting issues that prevented them from being imported into n8n. I've created corrected versions that are properly formatted and ready to import.

## ✅ Ready-to-Import Files

### 1. `gemini-simple-workflow.json` - **START HERE**
- **Status**: ✅ Ready to import
- **Best for**: Testing and learning
- **Features**: Clean, simple structure with core Gemini functionality
- **Nodes**: 6 nodes with clear flow
- **Complexity**: Low

### 2. `gemini-workflow-fixed.json` - **PRODUCTION READY**
- **Status**: ✅ Ready to import  
- **Best for**: Production use
- **Features**: Complete replacement of original workflow
- **Nodes**: Full feature set with all original functionality
- **Complexity**: High

## 🚀 Quick Import Instructions

### Step 1: Download the File
Choose either:
- `gemini-simple-workflow.json` (recommended for first-time users)
- `gemini-workflow-fixed.json` (for complete functionality)

### Step 2: Import into n8n
1. Open your n8n instance
2. Go to **Workflows** in the left sidebar
3. Click **Import from File** button
4. Select your downloaded JSON file
5. Click **Import**

### Step 3: Configure API Key
1. Go to **Settings** → **Environment Variables**
2. Add new variable:
   - **Name**: `GEMINI_API_KEY`
   - **Value**: Your Gemini API key from [Google AI Studio](https://ai.google.dev/)

### Step 4: Test the Workflow
1. Click **Execute Workflow** button
2. Check the execution results
3. Verify Gemini API responses

## 🔧 Configuration Requirements

### Environment Variables
```bash
GEMINI_API_KEY=your_gemini_api_key_here
```

### Google Sheets Credentials
- Ensure your Google Sheets OAuth2 credentials are configured
- Update document IDs if using different spreadsheets

### Input Data Format
Your Google Sheets should have these columns:
- `Query`: Main keyword/query
- `Query in English`: English translation
- `Output language`: Target output language
- `Status`: Set to "Run" to process

## 📊 Workflow Comparison

| Feature | Simple Workflow | Fixed Workflow |
|---------|----------------|----------------|
| Import Ready | ✅ | ✅ |
| Gemini Integration | ✅ | ✅ |
| Google Search Grounding | ✅ | ✅ |
| Search Intent Analysis | ✅ | ✅ |
| Competitor Analysis | ✅ | ✅ |
| Content Outline Generation | ❌ | ✅ |
| Google Docs Integration | ❌ | ✅ |
| Title/Slug/Meta Generation | ❌ | ✅ |
| MCP Integration | ❌ | ✅ |
| Complexity | Low | High |

## 🔍 What's Different from Perplexity

### API Endpoint
- **Old**: `https://api.perplexity.ai/chat/completions`
- **New**: `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent`

### Authentication
- **Old**: Perplexity API key in Authorization header
- **New**: Gemini API key in `X-goog-api-key` header

### Request Format
- **Old**: OpenAI-compatible chat format
- **New**: Gemini native format with Google Search grounding

### Response Parsing
- **Old**: `$json.choices[0].message.content`
- **New**: `$json.candidates[0].content.parts[0].text`

## 🛠 Troubleshooting

### Import Errors
- **Error**: "Invalid JSON format"
- **Solution**: Use `gemini-simple-workflow.json` or `gemini-workflow-fixed.json`

### API Key Errors
- **Error**: "API key not found"
- **Solution**: Set `GEMINI_API_KEY` environment variable

### Authentication Errors
- **Error**: "Invalid API key"
- **Solution**: Get new API key from [Google AI Studio](https://ai.google.dev/)

### Header Content Errors
- **Error**: "Invalid character in header content ["x-goog-api-key"]"
- **Cause**: Newline character (`\n`) or extra whitespace in API key
- **Solution**: Remove any trailing newlines or spaces from your API key value

### Google Search Grounding Errors
- **Error**: "google_search_retrieval is not supported; please use google_search field instead"
- **Cause**: Outdated Google Search grounding configuration
- **Solution**: Use the new `google_search` field format instead of `googleSearchRetrieval`

### Rate Limiting
- **Error**: "Too many requests"
- **Solution**: Add delay nodes between API calls

## 📈 Expected Results

### Search Intent Analysis
- Comprehensive analysis of user search intent
- Language-specific insights
- Competitor understanding

### Top 10 Analysis
- List of best-ranking articles
- Analysis of why they rank well
- Content gap identification

### Content Recommendations
- SEO-optimized content suggestions
- Structured outline recommendations
- Competitive advantage insights

## 🎯 Next Steps

1. **Start with Simple**: Import `gemini-simple-workflow.json` first
2. **Test Thoroughly**: Run with sample data to verify functionality
3. **Upgrade Gradually**: Move to `gemini-workflow-fixed.json` when ready
4. **Customize**: Modify workflows based on your specific needs
5. **Monitor**: Track API usage and costs

## 📞 Support

If you encounter issues:
1. Check the n8n execution logs for detailed error messages
2. Verify your Gemini API key is valid and has sufficient quota
3. Ensure Google Sheets credentials are properly configured
4. Test individual nodes to isolate problems

## 🔗 Useful Links

- [Google AI Studio](https://ai.google.dev/) - Get your Gemini API key
- [Gemini API Documentation](https://ai.google.dev/gemini-api/docs) - API reference
- [n8n Documentation](https://docs.n8n.io/) - n8n help and guides
