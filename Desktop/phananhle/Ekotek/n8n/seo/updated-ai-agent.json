{"nodes": [{"parameters": {"promptType": "define", "text": "=Based on the Google Helpful Content Guidelines, SEO best practices, and competitive analysis provided, create a comprehensive outline for the query \"{{ $('Google Sheets1').item.json.Query }}\".\n\nThe outline must include these required sections: [{{ $('Google Sheets1').item.json['outline focus'] }}]\n\n[Search Intent Analysis]:\n{{ $('Store Search Intent').item.json.searchIntentResult }}\n\n[Competitive Intelligence]:\n{{ $('Store Top 10 Analysis').item.json.top10AnalysisResult }}\n\n[Brand Authority Context]:\n- Brand: {{ $('Google Sheets1').item.json['Brand Name'] }}\n- Information: {{ $('Google Sheets1').item.json['Brand information'] }}\n- Solution: {{ $('Google Sheets1').item.json['Brand solution'] }}\n\nPlease provide a complete outline with detailed article methodology for each section that outperforms the competitor analysis.\n\n[Final Requirements]:\n1. Output should be in {{ $('Google Sheets1').item.json['Output language'] }}\n2. Use markdown format for easy copy & paste to Google Docs\n3. Include full checklists (20+ items) when applicable\n4. This is the year 2025\n5. Output contains ONLY the outline & article methodology", "hasOutputParser": true, "options": {"systemMessage": "=[Role]:\nYou are a Senior Semantic SEO Expert knowledgeable about topical authority, semantic content, and creating great SEO-friendly content that meets Google's Search Quality Evaluator Guidelines and Helpful Content Guidelines.\n\n[Tools Available]:\n- Gemini_Research_Tool: Research current information needed for article methodology (limit to 3 times)\n- SEO_Guidelines_Tool: Access comprehensive SEO guidelines and content quality frameworks\n- Think: Analyze and ensure output fulfills search intent and quality guidelines\n\n[Content Quality Framework]:\nI/ Structure & Flow:\n- Proceed from \"Outline focus\" ensuring contextual flow, vectors, hierarchy, and coverage\n- Satisfy user search intent from the provided analysis\n- One H1 as central theme\n- H2 sections support main theme\n- H3 sections break down H2 subtopics logically\n- Contextually coherent with smooth progression\n\nII/ Content Segmentation:\n- Main Content (80%+): Comprehensive primary topic coverage\n- Supplemental Content (<20%): Additional insights and perspectives\n- Seamless contextual bridges between sections\n\nIII/ Optimization Criteria:\n- First 10 headings answer pressing questions\n- Group related headings for consistency\n- Include Boolean, Definitional, Grouping, and Comparative questions\n- No conclusion in outline\n\nIV/ Authority Integration:\n- Demonstrate high expertise and credibility\n- Integrate brand authority naturally from provided context\n- Include case study opportunities\n- Surpass competitor content quality from analysis\n\nV/ Competitive Advantage:\n- Leverage insights from competitive analysis to outperform existing content\n- Address content gaps identified in competitor research\n- Include unique value propositions from brand context\n- Position content to demonstrate superior expertise\n\n[Article Methodology Requirements]:\nFor each section provide:\n- Content format (paragraphs, bullets, tables)\n- Word count estimates\n- Main ideas and coverage requirements\n- Examples, data, evidence to include\n- Section connections and flow\n- Authority integration opportunities\n- Competitive differentiation elements"}}, "id": "dde0f605-242c-4a41-a93c-e95627f59fc6", "name": "Enhanced Content Outline Generator", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1024, 160]}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1.1, "position": [1248, 368], "id": "59933c0b-cb28-4aa7-a92b-c87947cc0ef3", "name": "Think"}, {"parameters": {"model": {"__rl": true, "value": "gemini-2.0-flash", "mode": "list", "cachedResultName": "Gemini 2.0 Flash"}, "options": {"maxTokensToSample": 64000, "temperature": 0.8}}, "type": "@n8n/n8n-nodes-langchain.lmChatGemini", "typeVersion": 1.0, "position": [912, 384], "id": "8a175554-51e4-4086-955b-09e29521f691", "name": "Gemini Language Model", "credentials": {"geminiApi": {"id": "your-gemini-credentials-id", "name": "Gemini API"}}}, {"parameters": {"toolDescription": "Research current and accurate information needed for article methodology to create great semantic content", "method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-goog-api-key", "value": "={{ $vars.GEMINI_API_KEY }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"Research and provide current, accurate information for: {query}\\n\\nFocus on:\\n- Latest SEO best practices and guidelines\\n- Current industry trends and data\\n- Authoritative sources and expert insights\\n- Practical examples and case studies\\n- Technical implementation details\\n- Brand authority building strategies\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 0.8,\n    \"maxOutputTokens\": 20000\n  },\n  \"tools\": [\n    {\n      \"google_search\": {}\n    }\n  ]\n}", "placeholderDefinitions": {"values": [{"name": "query", "description": "Research query for current SEO information and best practices", "type": "string"}]}}, "id": "1ffbf548-2bf5-445d-8ea2-ab52660b405e", "name": "Gemini_Research_Tool", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1024, 384]}, {"parameters": {"toolDescription": "Access comprehensive SEO guidelines, content quality checklists, and article methodology frameworks", "method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-goog-api-key", "value": "={{ $vars.GEMINI_API_KEY }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"Provide comprehensive SEO content guidelines and checklists based on:\\n\\n1. Google's Search Quality Evaluator Guidelines\\n2. Google's Helpful Content Guidelines\\n3. E-E-A-T principles (Experience, Expertise, Authoritativeness, Trustworthiness)\\n4. Content structure and methodology best practices\\n5. Semantic SEO and topical authority frameworks\\n6. Competitive content analysis principles\\n\\nInclude:\\n- Content structure requirements\\n- Quality assessment criteria\\n- Authority building strategies\\n- User intent fulfillment methods\\n- Technical SEO considerations\\n- Content methodology frameworks\\n- Competitive differentiation strategies\\n\\nFocus on creating content that demonstrates expertise, builds trust, satisfies user search intent, and outperforms competitor content while following Google's quality guidelines.\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 0.7,\n    \"maxOutputTokens\": 25000\n  },\n  \"tools\": [\n    {\n      \"google_search\": {}\n    }\n  ]\n}", "placeholderDefinitions": {"values": []}}, "id": "0103fb85-14db-4388-a16e-ebbba3d7bce1", "name": "SEO_Guidelines_Tool", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1136, 384]}], "connections": {"Think": {"ai_tool": [[{"node": "Enhanced Content Outline Generator", "type": "ai_tool", "index": 0}]]}, "Gemini Language Model": {"ai_languageModel": [[{"node": "Enhanced Content Outline Generator", "type": "ai_languageModel", "index": 0}]]}, "Gemini_Research_Tool": {"ai_tool": [[{"node": "Enhanced Content Outline Generator", "type": "ai_tool", "index": 0}]]}, "SEO_Guidelines_Tool": {"ai_tool": [[{"node": "Enhanced Content Outline Generator", "type": "ai_tool", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "ac5d7c077b33a009e2c00b5e3cc656daf6c625eeb3a2dbea6944510bb25fc9ec"}}