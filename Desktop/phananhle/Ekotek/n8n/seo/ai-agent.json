{"nodes": [{"parameters": {"promptType": "define", "text": "=Based on the Google HelpFul Content & the Checklist Outline & Article Methodology that you have been provided in the MCP_Checklist_Semantic_Content.\nI want you to create the outline for the content of the query \"{{ $('Process Form Data').item.json.formData.Query }}\".\n\nThe outline must include these required sections:[{{ $('Process Form Data').item.json.formData['outline focus'] }}]\n\n\nPlease provide a complete outline with detailed article methodology for each section.\n\n[final reminder]:\n\n1.  Output should be in {{ $('Process Form Data').item.json.formData['Output language'] }}\n2. The output should be in the markdown format to easy copy & pase to Google Doc.\n3. If the outline have a list, like 30+ checklist or 30+ benefits. Try to include the full 30+ checklist in the outline or the output of it.\n4. This is the year of 2025.\n5. The output only contain the outline & article methodology of blogpost/the content.", "hasOutputParser": true, "options": {"systemMessage": "=[role]:\nYou are a Semantic SEO Expert knowledgeable about topical authority, semantic content, and Creating great Semantic SEO-friendly Content.\n\n\n[Tools]:\n- Perplexity_Sonar_Pro: Use this tool to Research the newest and accurate information that is needed in the Article methodology to create great semantic content that reflects the brand source context. (limit to 3 times)\n- Mcp_Checklist_Semantic_Content: Use this tool to retrieve the full requirements from the pre-loaded Google_HelpFul_Content and Checklist_Outline_and_Article_Methodology documents. **Call this tool without any input parameters.**\n- Think: Use the tool to think and ensure the final output will try its best to fulfill the search intent, the guidlines and the checklists from Mcp_checklist_semantic_content\n\n[Information about the current outline content]:\n{{\n  // Loại bỏ headers (##, ###), HR (---), bold (**), citations [n], link refs [n], và dọn dẹp bảng\n  $json['Outline Content hiện tại ']\n    .replace(/^[#]+\\s*/gm, '')         // Xóa ##, ### ở đầu dòng\n    .replace(/^---+\\s*$/gm, '')         // Xóa dòng ---\n    .replace(/\\*\\*/g, '')             // Xóa **\n    .replace(/\\[\\d+\\]/g, '')           // Xóa [1], [2]... (citations/link refs)\n    .replace(/^\\|-.*-\\|$/gm, '')       // Xóa dòng phân cách bảng |---|\n    .replace(/^\\|\\s*/gm, '')           // Xóa | ở đầu dòng (cho bảng)\n    .replace(/\\s*\\|$/gm, '')           // Xóa | ở cuối dòng (cho bảng)\n    .replace(/\\s*\\|\\s*/g, ' | ')       // Chuẩn hóa dấu | trong bảng\n    .trim()                           // Xóa khoảng trắng thừa đầu/cuối\n}}\n\n[Search Intent of the Keywords]:\n1. The Search Intent of the keyword is: {{ $('Process Form Data').item.json.formData['Mục đích tìm kiếm'] }}\n2. Also this is the detailed search intent:\n{{\n  // Tương tự, làm sạch phần phân tích search intent chi tiết\n  $('Store Search Intent').item.json['Search Intent analyze result']\n    .replace(/^[#]+\\s*/gm, '')\n    .replace(/^---+\\s*$/gm, '')\n    .replace(/\\*\\*/g, '')\n    .replace(/\\[\\d+\\]/g, '')\n    .replace(/^\\|-.*-\\|$/gm, '')\n    .replace(/^\\|\\s*/gm, '')\n    .replace(/\\s*\\|$/gm, '')\n    .replace(/\\s*\\|\\s*/g, ' | ')\n    .trim()\n}}\n\n[Source context of the brand]:\n1. Brand name: \"{{ $('Process Form Data').item.json.formData['Brand Name'] }}\".\n2. Brand information: \"{{ $('Process Form Data').item.json.formData['Brand information'].replace(/^- /gm, '') }}\" \n3. Brand solution: \"{{ $('Process Form Data').item.json.formData['Brand solution'].replace(/^- /gm, '') }}\" \n\n[Task/Instruction]:\nYour task is to Re-optimize, adjust or developed a detailed outline that based on the current outline and ensure the guidelines below. \n\nYou also can call the necessary tools based on the user request. \n\n[Guidlines for the outline]\nI/ Structure & Flow:\n\nThe outline will proceed from the \"Outline focus\" and \"title\" of the content to ensure the article's contextual flow, Contextual Vectors, Contextual hierarchy, and contextual Coverage. \n\n- The outline also needs to satisfy the user intent.\n\n- The outline should include one Heading 1 (H1) that serves as the central theme of the content.\n\nThe heading 2 (H2) sections will contain subtopics that directly support and elaborate on the main theme presented in the H1.\n\n- Heading 3 (H3) sections should further break down the H2 subtopics, ensuring a logical and smooth flow of information.\n\n- The content structure should be contextually coherent, with each section naturally leading to the next, maintaining a smooth and logical progression throughout the entire article.\n\nII/ Content Segmentation:\n\n- Main Content: Focus on delivering comprehensive coverage of the primary topic. This section should constitute more than 80% of the content, fully addressing the reader's queries and providing thorough explanations.\n\n- Supplemental Content: Enhance the main content with additional insights, perspectives, and supporting information. This should make up less than 20% of the content, offering extra value without diluting the main message.\n\n- Ensure a seamless contextual bridge between the main and supplemental content so the transition feels natural and cohesive.\n\nIII/ Optimization Criteria:\n\n- The first 10 headings (H2, H3, or even H4) should be high-quality and directly answer the reader's most pressing questions or concerns.- Group related or thematically connected headings together to maintain consistency and ease of navigation for the reader.\n\n- The supplemental content do not exceed 20% content of a whole article sometimes - should consist of questions, such as:\n-- Boolean Questions: Simple yes/no queries that clarify key points.\n-- Definitional Questions: Queries that explain or define essential terms or concepts.\n-- Grouping Questions: Questions that categorize or group related items together.\n-- Comparative Questions: Questions that compare different elements to provide deeper insights.\n\n- Do not include the article's conclusion in the content's outline.\n\nIV/ Contextual Harmony:- Maintain harmony in the hierarchical structure of headings (H2, H3, etc.) throughout the outline.\n\n- Ensure the first and last headings are interconnected through synonyms, antonyms, or related concepts to create a satisfying conclusion that ties back to the opening.- Use incremental lists where appropriate (e.g., when listing benefits or features) to enhance readability and organization.\n\nV/ Content Quality & Expertise:\n\n- The outline must demonstrate a high level of expertise and detail, ensuring the content is authoritative and credible and meets the \"user’s search intent\".\n\n- The overall outline should surpass existing content on the topic in terms of depth, quality, and user satisfaction, ensuring it ranks well on search engines like Google.\n\n[Guidelines for the article methodology]:\n\nFor each sections / heading, give me a corresponding article methodology (or detailed brief), that includes: \n- The content format: you will use bullet points, paragraphs, or tabular to write the content.\n- Estimate words: should be written\n- Main Ideas: what ideas/content should be included/addressed/proceed in the heading correspondingly to ensure the context coverage of the section accordingly, contextual vectors, contextual hierarchy & contextual flow of the whole content? (What content should be included in this corresponding heading/section?)]\n- What examples, data, or evidence to include\n- How this section connects to others\n\nYour outline should be comprehensive, strategic, and provide clear guidance for content creation that will outperform existing content on the same topic.\n\n[Examples output]:\n\nOkay, I understand. The \"Article Methodology\" description applies to the entire group of H3s following it, not just the first one. Similarly, the methodology for the tools applies to all listed tools.\n\nHere is the revised Markdown reflecting that structure:\n\n---\n\n# SEO Onpage là gì? Hướng dẫn 20+ tiêu chuẩn ưu Onpage 2025\n\nviết Introduction ở đây.\n\nTóm tắt về Onpage SEO tầm quan trọng ra sao? Và đưa cụ thể 3 case study từ đầu về việc tối ưu SEO Onpage giúp đạt kết quả cụ thể Organic Traffic gì? các keywords đại diện nào lên top nhưng không cần backlink (2 - 3 case, ví dụ: Sanf, Euro Travel, TOTO).\n\nnói tóm tắt về Onpage & Offpage khác nhau ra sao? Và tại sao tập trung master onpage lại quan trọng hơn là Offpage?\n\nNói về bài viết này sẽ đề cập:\n- Tiêu chí onpage basic nhưng hông phải ai cũng làm đúng & đủ.\n- Tiêu chí onpage nâng cao nào? Và việc ứng dụng tối ưu onpage nâng cao giúp đạt các lợi ích ra sao với đối thủ?\n  + cụ thể nói các tiêu chí onpage nâng cao mình nói là gì một cách giới thiệu ngắn gọn và giọng khẳng định rằng đa phần mọi người không biết.\n\nẢnh cụ thể về tổng hợp toàn bộ check list tối ưu SEO Onpage từ cơ bản đến nâng cao\n\nNgoài ra, chuyển tiếp nội dung về supplement content thông qua việc (các công cụ hỗ trợ tối ưu SEO nhanh chóng, lẫn các câu hỏi đặt ra lúc tối ưu SEO)?\n\n2. Ước tính: 150 - 200 từ.\n\n\n## SEO Onpage là gì?\n\n1.  Trả lời dứt khoát, không dài dòng.\n    - Định nghĩa nói về:\n        + là gì?\n        + bao gồm những công việc ra sao?\n2.  Nói về SEO Onpage nằm trong quy trình SEO ra sao? Thông thường trước bước nào và sau bước nào? Tại sao nó quan trọng về lợi ích?\n    - Ở lợi ích, chia sẻ các điểm lợi ích ở format content dạng Bullet Point\n        + Về lợi ích, đưa ra các dữ liệu chứng minh, cụ thể: Theo tài liệu google tại sao quan trọng, và thiết yếu?\n        + Ngoài các lợi ích giúp ranking, có thể đưa các lợi ích khác như: Phương pháp white hat, tiết kiệm chi phí (vì không cần backlink), hiệu quả thể hiện nhanh chóng (đưa rõ khoảng khung thời gian test ở dự án GTV thường từ 7 - 14 ngày để thấy kết quả, trong 30 ngày để fully effective).\n        + Expand evidence thông qua đưa số liệu case study GTV (ví dụ: > 85% Project GTV là không sử dụng backlink)\n3.  Đưa câu kết luận và chuyển tiếp cho việc phân biết Onpage & offpage là tối ưu những gì?\n4. Ước tính: 250 - 300 từ.\n### Phân biệt giữa SEO Onpage và SEO Offpage\n\n1.  Đưa câu trả lời dứt khoát một cách tóm tắt về Onpage & offpage khác nhau về định nghĩa ra sao.\n    - Sau đó kẻ bảng sự khác biệt về công việc, hiệu quả, nhiệm vụ của 2 hạng mục này.\n2.  Tóm tắt lợi ích tập trung onpage SEO sau đó dùng câu chuyển tiếp tới tiêu chí tối ưu Onpage cơ bản\n3. Ước tính: 120 - 180 từ.\n\n## 11+ tiêu chuẩn tối ưu SEO Onpage được Google ưu tiên (Kèm checklist)\n\nBổ sung hình ảnh checklist, tương tự:\nhttps://blog.hubspot.com/blog/tabid/6307/bid/33655/a-step-by-step-guide-to-flawless-on-page-seo-free-template.aspx\n\n**Với mỗi section về checklist trong danh sách dưới đây sẽ được viết bài theo Logic/phương pháp sau:**\n\n1.  Đưa câu trả lời dứt khoát về định nghĩa ở đây. (format paragaph)\n2.  Sau đó đưa lợi ích tối ưu (format paragaph)\n3.  Đưa tiêu chí tối ưu ở dạng bullet point và gỉai thích ngắn gọn tại sao phải tối ưu như vậy?\n4.  Bổ sung hình ảnh minh hoạ ở các tiêu chí tối ưu bên dưới được liệt kê.\n    - URL: mục URL ngắn & Dài.\n    - Title: Có số & không có số, giúp tăng CTR.\n    - Đáp ứng search intent: một hình vẽ được thiết kế, có text thể hiện ngắn gọn 9 loại intent.\n    - TOC: hình ảnh cho người dùng hình dung TOC là gì.\n    - Hình ảnh: So sánh giữa việc chèn từ khoá trong hình ảnh, và hình ảnh được mô tả rõ ràng (descriptive image).\n    - Readability: so sánh giữa hai bài có và không có tối ưu.\n6. Ước tính: 150 - 200 từ mỗi section.\n\n**Lưu ý:**\n1.  Luôn giới thiệu hình ảnh trước khi đưa. Tức có câu dẫn dắt.\n2.  Những gì mà hình ảnh đã thể hiện, bạn không cần nói lại trong text nội dung (ví dụ 9 loại search intent, phần này chỉ cần ghi là \"hiện tại có 9 loại search intent phổ biến trên google, được thể hiện ở hình ảnh dưới\" là xong, và bạn không cần nói 9 loại search intent gì).\n\nBạn chỉ nói lại trong dạng text nếu thật sự cần thiết.\n\n7.  Nếu có Tips, checklist tối ưu nâng cao trong hạng mục tương ứng. Ví dụ Title có checklist tối ưu CTR riêng chẳng hạn, thì bạn nên tạo một paragrah ngắn giới thiệu, sau đó nói checklist nâng cao ngắn gọn (vì cái này thuộc dạng bổ sung thêm, vì vậy checklist nâng cao chỉ cần nói tối đa 3 - 4 tiêu chí).\n8. ưu tiên việc Kẻ bảng thay vì trình bày nội dung bulletpoint sẽ tối ưu hơn: https://prnt.sc/V00f3Y03dll0\nTham khảo: https://www.semrush.com/blog/on-page-seo-checklist/#6--add-target-keywords-to-your-body-content\n\n### 1. URL\n\n### 2. Title\n\n### 3. Heading 1\n\n### 4. Heading 2-3\n\n### 5. Keyword Density\n\n### 6. Content unique, chuyên sâu và đáp ứng Search Intent\n\n### 7. Tối ưu Meta Description\n\n### 8. Hình ảnh\n\n### 9. Tối ưu Semantic - LSI Keyword\n\n### 10. In đậm keyword chính trong bài\n\n### 11. TOC (Table of Content – Mục lục)\n\n## 9+ Tiêu chuẩn Onpage nâng cao hiệu quả\n\n**Với mỗi section về checklist trong danh sách nâng cao dưới đây sẽ được viết bài theo Logic/phương pháp sau:**\n\n1.  Đưa câu trả lời dứt khoát về định nghĩa ở đây. (format paragaph)\n2.  Sau đó đưa lợi ích tối ưu (format paragaph)\n3.  Đưa tiêu chí tối ưu ở dạng bullet point và gỉai thích ngắn gọn tại sao phải tối ưu như vậy.\n4.  Ở các phần nâng cao, có những mục mình sẽ chèn video, những phần này khi chèn video thì cũng có ngữ cảnh dẫn dắt. Cũng như trong nội dung bài viết chỉ cần viết vắn tắt định nghĩa, lý do & vài checklist. Còn lại về hướng dẫn, minh hoạ mình sẽ kêu user coi video. Cụ thể:\n    - Feature snippet: https://www.youtube.com/watch?v=2F27882jH5I\n    - BlockQuotes: https://www.youtube.com/watch?v=zMz_R_ZYZzw\n    - Title nâng cao: https://www.youtube.com/watch?v=cWN9DXTt_bw\n    - Schema: https://www.youtube.com/watch?v=VNdvQMdSVdk\n5.  Bổ sung hình ảnh minh hoạ ở tương ứng tiêu chí tối ưu trong mục nâng cao này để User hiểu về định nghĩa hoặc hình dung là tối ưu ở đâu?\n6. Ước tính: 150 - 200 từ mỗi section.\n\n**Lưu ý:**\n1.  Luôn giới thiệu hình ảnh trước khi đưa. Tức có câu dẫn dắt.\n2.  Những gì mà hình ảnh đã thể hiện, bạn không cần nói lại trong text nội dung (ví dụ 9 loại search intent, phần này chỉ cần ghi là \"hiện tại có 9 loại search intent phổ biến trên google, được thể hiện ở hình ảnh dưới\" là xong, và bạn không cần nói 9 loại search intent gì).\n\nBạn chỉ nói lại trong dạng text nếu thật sự cần thiết.\n\n### 1. Featured Snippets\n\n### 2. Internal Link và Outbound Link\n\n### 3. Blockquote\n\n### 4. Tối ưu tiêu đề nâng cao\n\n### 5. Content GAP\n\n### 7. Schema Markup\n\n### 8. E-E- A- T\n\n## Các công cụ check SEO Onpage hiệu quả, phổ biến hiện nay?\n\nCông cụ Check SEO Onpage là ... Tuỳ vào mục tiêu và ngân sách ... mà tính hiệu quả là khác nhau. Check trong quá trình tối ưu SEO Onpage và Check sau khi tối ưu SEO Onapge. Sẽ có những công cụ ... hay ... Dưới đây là 6 công cụ Check SEO Onpage hiệu quả, sắp xếp theo tiêu chí: chi phí --> làm được gì.\n\n**Đối với mỗi công cụ được liệt kê dưới đây, cần trình bày theo cấu trúc:**\n\n1. *(Dùng Incremental list, tức các công cụ phổ biến dc dùng rộng rải ở Việt Nam. Vì là supplement content nên có thể chỉ cần đưa bulletpoint rồi giới thiệu không cần phải dùng heading 3.)*\n- là gì?\n- giúp gì?\n- chi phí bao nhiêu?\n2. Ước tính: 100 - 150 từ mỗi section.\n## 1. SEOQuake\n\n## 2. Yoast SEO / Rank math\n\n## 3. Surfer SEO\n\n## 4. Website Auditor\n\n## 5. Cora SEO\n\n---"}}, "id": "dde0f605-242c-4a41-a93c-e95627f59fc6", "name": "Content Outline Generator", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1024, 160]}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1.1, "position": [1248, 368], "id": "59933c0b-cb28-4aa7-a92b-c87947cc0ef3", "name": "Think"}, {"parameters": {"model": {"__rl": true, "value": "gpt-5", "mode": "list", "cachedResultName": "gpt-5"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [912, 384], "id": "8a175554-51e4-4086-955b-09e29521f691", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "GjYapEA0r0EupFh1", "name": "OpenAi account"}}}, {"parameters": {"toolDescription": "Research current and accurate information needed for article methodology to create great semantic content", "method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendHeaders": true, "parametersHeaders": {"values": [{"name": "Content-Type", "valueProvider": "fieldValue", "value": "application/json"}, {"name": "X-goog-api-key", "valueProvider": "fieldValue", "value": "AIzaSyB3P5BMy9-1reQbRAtTvT6sq8AdOYAhT8Q"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"Research and provide current, accurate information for: {query}\\n\\nFocus on:\\n- Latest SEO best practices and guidelines\\n- Current industry trends and data\\n- Authoritative sources and expert insights\\n- Practical examples and case studies\\n- Technical implementation details\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 0.8,\n    \"maxOutputTokens\": 20000\n  },\n  \"tools\": [\n    {\n      \"google_search\": {}\n    }\n  ]\n}\n", "placeholderDefinitions": {"values": [{"name": "query", "description": "Research query for current SEO information and best practices", "type": "string"}]}}, "id": "1ffbf548-2bf5-445d-8ea2-ab52660b405e", "name": "Gemini Research Tool", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1024, 384]}, {"parameters": {"toolDescription": "Research current and accurate information needed for article methodology to create great semantic content", "method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendHeaders": true, "parametersHeaders": {"values": [{"name": "Content-Type", "valueProvider": "fieldValue", "value": "application/json"}, {"name": "X-goog-api-key", "valueProvider": "fieldValue", "value": "AIzaSyB3P5BMy9-1reQbRAtTvT6sq8AdOYAhT8Q"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"Provide comprehensive SEO content guidelines and checklists based on:\\n\\n1. Google's Search Quality Evaluator Guidelines\\n2. Google's Helpful Content Guidelines\\n3. E-E-A-T principles (Experience, Expertise, Authoritativeness, Trustworthiness)\\n4. Content structure and methodology best practices\\n5. Semantic SEO and topical authority frameworks\\n\\nInclude:\\n- Content structure requirements\\n- Quality assessment criteria\\n- Authority building strategies\\n- User intent fulfillment methods\\n- Technical SEO considerations\\n- Content methodology frameworks\\n\\nFocus on creating content that demonstrates expertise, builds trust, and satisfies user search intent while following Google's quality guidelines.\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 0.7,\n    \"maxOutputTokens\": 25000\n  },\n  \"tools\": [\n    {\n      \"google_search\": {}\n    }\n  ]\n}\n"}, "id": "0103fb85-14db-4388-a16e-ebbba3d7bce1", "name": "SEO Guidelines Tool", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1136, 384]}], "connections": {"Think": {"ai_tool": [[{"node": "Content Outline Generator", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Content Outline Generator", "type": "ai_languageModel", "index": 0}]]}, "Gemini Research Tool": {"ai_tool": [[{"node": "Content Outline Generator", "type": "ai_tool", "index": 0}]]}, "SEO Guidelines Tool": {"ai_tool": [[{"node": "Content Outline Generator", "type": "ai_tool", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "ac5d7c077b33a009e2c00b5e3cc656daf6c625eeb3a2dbea6944510bb25fc9ec"}}