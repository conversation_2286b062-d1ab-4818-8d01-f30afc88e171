{
  "meta": {
    "instanceId": "gemini-seo-workflow-2025"
  },
  "nodes": [
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "field": "hours"
            }
          ]
        }
      },
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.2,
      "position": [
        0,
        0
      ],
      "id": "de354698-2def-465f-89ec-abbf0fd75386",
      "name": "Schedule Trigger"
    },
    {
      "parameters": {
        "documentId": {
          "__rl": true,
          "value": "1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM",
          "mode": "list",
          "cachedResultName": "Ekotek | Workflow Automation N8n - Outline & Keywords Update",
          "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM/edit?usp=drivesdk"
        },
        "sheetName": {
          "__rl": true,
          "value": 192143138,
          "mode": "list",
          "cachedResultName": "Sheet1",
          "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM/edit#gid=192143138"
        },
        "filtersUI": {
          "values": [
            {
              "lookupColumn": "Status",
              "lookupValue": "Run"
            }
          ]
        },
        "options": {}
      },
      "type": "n8n-nodes-base.googleSheets",
      "typeVersion": 4.5,
      "position": [
        224,
        0
      ],
      "id": "a14ca7e1-a133-47b0-8e1a-9c72736e1b81",
      "name": "Google Sheets Input",
      "alwaysOutputData": true,
      "credentials": {
        "googleSheetsOAuth2Api": {
          "id": "wQQPRkUeeGCPHnlK",
          "name": "Google Sheets account"
        }
      }
    },
    {
      "parameters": {
        "assignments": {
          "assignments": [
            {
              "id": "5e8d95c1-407c-4adc-b61a-a8ded2aed84a",
              "name": "formData",
              "type": "object",
              "value": "={{ $json }}"
            },
            {
              "id": "31fe2c22-f140-4a89-8f98-af0eff940e82",
              "name": "projectId",
              "type": "string",
              "value": "={{ $now.valueOf() }}"
            }
          ]
        },
        "options": {}
      },
      "id": "8082186e-d9ff-4dee-b8cd-274c1a8a70f6",
      "name": "Process Form Data",
      "type": "n8n-nodes-base.set",
      "typeVersion": 3.4,
      "position": [
        432,
        0
      ]
    },
    {
      "parameters": {
        "method": "POST",
        "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent",
        "authentication": "genericCredentialType",
        "genericAuthType": "httpHeaderAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            },
            {
              "name": "X-goog-api-key",
              "value": "={{ $vars.GEMINI_API_KEY }}"
            }
          ]
        },
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"[Instruction]: Answer in Markdown format with clear structure, bullet points, and short paragraphs.\\n\\n[Role]: You are a Researcher & Semantic SEO Expert who is a master of the Topical Authority Concept from Koray. Your task is to analyze the Search Intent behind the query.\\n\\nAnalyze the query: [{{ $json.formData.Query }} which means {{ $json.formData['Query in English'] }}]; what would they be looking for if they searched it? What is the search intent on Google and how to fulfill it? Search and compare both *{{$('Process Form Data').item.json.formData['Output language']}}* & English articles, but prioritize *{{$('Process Form Data').item.json.formData['Output language']}}*.\\n\\n### Output Language:\\n*{{$('Process Form Data').item.json.formData['Output language']}}*\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 1,\n    \"maxOutputTokens\": 10000\n  },\n  \"tools\": [\n    {\n      \"googleSearchRetrieval\": {\n        \"dynamicRetrievalConfig\": {\n          \"mode\": \"MODE_DYNAMIC\",\n          \"dynamicThreshold\": 0.7\n        }\n      }\n    }\n  ]\n}",
        "options": {}
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        640,
        0
      ],
      "id": "d6d28b23-3a33-4015-ae3f-d18bbc360524",
      "name": "Gemini Search Intent Analysis"
    },
    {
      "parameters": {
        "assignments": {
          "assignments": [
            {
              "id": "4fc7d4cb-b109-4202-b75e-57fe1715e33d",
              "name": "Search Intent analyze result",
              "value": "={{ $json.candidates[0].content.parts[0].text }}",
              "type": "string"
            }
          ]
        },
        "options": {
          "ignoreConversionErrors": true
        }
      },
      "id": "74ed4f53-d078-4668-b886-7a4a10486567",
      "name": "Store Search Intent",
      "type": "n8n-nodes-base.set",
      "typeVersion": 3.4,
      "position": [
        864,
        0
      ]
    },
    {
      "parameters": {
        "method": "POST",
        "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent",
        "authentication": "genericCredentialType",
        "genericAuthType": "httpHeaderAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            },
            {
              "name": "X-goog-api-key",
              "value": "={{ $vars.GEMINI_API_KEY }}"
            }
          ]
        },
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"[Knowledge about User's Search Intent]: {{ JSON.stringify($json['Search Intent analyze result']).slice(1, -1) }}\\n\\n[Instruction]: Answer in Markdown format with clear structure, bullet points, and short paragraphs.\\n\\n[Role]: You are a Semantic SEO Expert who is a master of the Topical Authority Concept from Koray. Your task is to analyze the competitor's content outline with the provided 'User's Search Intent' in mind.\\n\\n## Task:\\nIdentify the 10 best online articles that answer the query: *{{ $('Process Form Data').item.json.formData.Query }}* which means {{ $('Process Form Data').item.json.formData['Query in English'] }}, and satisfy the search intent that you have been provided\\n\\n### Requirements:\\n- Explain WHY these articles are the best.\\n- EXCLUDE any video content.\\n- Search and compare both *{{$('Process Form Data').item.json.formData['Output language']}}* & English articles, but prioritize *{{$('Process Form Data').item.json.formData['Output language']}}*.\\n\\n### Output Language:\\n*{{$('Process Form Data').item.json.formData['Output language']}}*\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 1,\n    \"maxOutputTokens\": 32000\n  },\n  \"tools\": [\n    {\n      \"googleSearchRetrieval\": {\n        \"dynamicRetrievalConfig\": {\n          \"mode\": \"MODE_DYNAMIC\",\n          \"dynamicThreshold\": 0.7\n        }\n      }\n    }\n  ]\n}",
        "options": {}
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        1104,
        0
      ],
      "id": "f5abf022-8c09-4e1f-86f8-96d52ab56130",
      "name": "Gemini Top 10 Analysis"
    },
    {
      "parameters": {
        "assignments": {
          "assignments": [
            {
              "id": "40e327aa-3eaf-4cd7-b59e-8273b3c05355",
              "name": "Top 10 Analysis Result",
              "value": "={{ $json.candidates[0].content.parts[0].text }}",
              "type": "string"
            }
          ]
        },
        "options": {}
      },
      "type": "n8n-nodes-base.set",
      "typeVersion": 3.4,
      "position": [
        1312,
        0
      ],
      "id": "e982bbc9-f5aa-45db-a84d-bd9f7e56cec6",
      "name": "Store Top 10 Analysis"
    },
    {
      "parameters": {
        "method": "POST",
        "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent",
        "authentication": "genericCredentialType",
        "genericAuthType": "httpHeaderAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            },
            {
              "name": "X-goog-api-key",
              "value": "={{ $vars.GEMINI_API_KEY }}"
            }
          ]
        },
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"[Knowledge about User's Search Intent]:{{ $('Store Search Intent').item.json['Search Intent analyze result'].replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"').replace(/\\n/g, '\\\\n') }}\\n\\n[Competitor's outline information]:{{ JSON.stringify($json['Top 10 Analysis Result']).slice(1, -1) }}\\n\\n[Instruction]: Answer in Markdown format with clear structure, bullet points, and short paragraphs.\\n\\n[Role]: You are a Semantic SEO Expert knowledgeable about topical authority, semantic content, and Creating great SEO-friendly Content that meets the \\\"Search Quality Evaluator Guideline\\\", \\\"Google helpful content\\\" & \\\"google product review guideline\\\".\\n\\nStart by reading these documents: \\n-http://static.googleusercontent.com/media/guidelines.raterhub.com/en//searchqualityevaluatorguidelines.pdf \\n\\n-https://developers.google.com/search/docs/fundamentals/creating-helpful-content?hl=en \\n\\nThen, analyze these Articles that rank at the top for the keywords [{{ $('Google Sheets Input').item.json.Query.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"').replace(/\\n/g, '\\\\n') }}]: \\n\\n{{ $('Google Sheets Input').item.json['Top 3 URL'].replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"').replace(/\\n/g, '\\\\n') }}\\n\\nBased strictly on the guidelines or principles outlined in the document and the "Search Quality Evaluator Guideline" PDF from Google, analyze **these articles and the top 10 articles** above. Then, compare them in terms of depth and details of the content, the demonstration of expertise and credibility, and how well they fulfill the user's intent. \\n\\nI want you to create the outline for the content of the keywords I've provided. The outline has to be better than the competitor's or at least as good as theirs.\\n\\n\\n### [the output is in {{ $('Google Sheets Input').item.json['Output language'].replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"').replace(/\\n/g, '\\\\n') }} and only have the Final outline]\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 1,\n    \"maxOutputTokens\": 32000\n  },\n  \"tools\": [\n    {\n      \"googleSearchRetrieval\": {\n        \"dynamicRetrievalConfig\": {\n          \"mode\": \"MODE_DYNAMIC\",\n          \"dynamicThreshold\": 0.7\n        }\n      }\n    }\n  ]\n}",
        "options": {}
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        32,
        368
      ],
      "id": "9d784e4a-b3f6-4453-84c5-1e0b11c32e6e",
      "name": "Gemini Create Outline"
    },
    {
      "parameters": {
        "assignments": {
          "assignments": [
            {
              "id": "40e327aa-3eaf-4cd7-b59e-8273b3c05355",
              "name": "Final Outline Content",
              "value": "={{ $json.candidates[0].content.parts[0].text }}",
              "type": "string"
            }
          ]
        },
        "options": {}
      },
      "type": "n8n-nodes-base.set",
      "typeVersion": 3.4,
      "position": [
        224,
        368
      ],
      "id": "fc6ba16f-29ea-4c89-ad63-dfce35144bbc",
      "name": "Store Final Outline"
    }
  ],
  "connections": {
    "Schedule Trigger": {
      "main": [
        [
          {
            "node": "Google Sheets Input",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Google Sheets Input": {
      "main": [
        [
          {
            "node": "Process Form Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Process Form Data": {
      "main": [
        [
          {
            "node": "Gemini Search Intent Analysis",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Gemini Search Intent Analysis": {
      "main": [
        [
          {
            "node": "Store Search Intent",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Store Search Intent": {
      "main": [
        [
          {
            "node": "Gemini Top 10 Analysis",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Gemini Top 10 Analysis": {
      "main": [
        [
          {
            "node": "Store Top 10 Analysis",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Store Top 10 Analysis": {
      "main": [
        [
          {
            "node": "Gemini Create Outline",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Gemini Create Outline": {
      "main": [
        [
          {
            "node": "Store Final Outline",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  }
}
