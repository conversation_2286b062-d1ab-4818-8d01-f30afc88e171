{"nodes": [{"parameters": {"assignments": {"assignments": [{"id": "5e8d95c1-407c-4adc-b61a-a8ded2aed84a", "name": "formData", "type": "object", "value": "={{ $json }}"}, {"id": "31fe2c22-f140-4a89-8f98-af0eff940e82", "name": "projectId", "type": "string", "value": "={{ $now.valueOf() }}"}]}, "options": {}}, "id": "8082186e-d9ff-4dee-b8cd-274c1a8a70f6", "name": "Process Form Data", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [432, 0]}, {"parameters": {"promptType": "define", "text": "=Based on the Google HelpFul Content & the Checklist Outline & Article Methodology that you have been provided in the MCP_Checklist_Semantic_Content.\nI want you to create the outline for the content of the query \"{{ $('Process Form Data').item.json.formData.Query }}\".\n\nThe outline must include these required sections:[{{ $('Process Form Data').item.json.formData['outline focus'] }}]\n\n\nPlease provide a complete outline with detailed article methodology for each section.\n\n[final reminder]:\n\n1.  Output should be in {{ $('Process Form Data').item.json.formData['Output language'] }}\n2. The output should be in the markdown format to easy copy & pase to Google Doc.\n3. If the outline have a list, like 30+ checklist or 30+ benefits. Try to include the full 30+ checklist in the outline or the output of it.\n4. This is the year of 2025.\n5. The output only contain the outline & article methodology of blogpost/the content.", "hasOutputParser": true, "options": {"systemMessage": "=[role]:\nYou are a Semantic SEO Expert knowledgeable about topical authority, semantic content, and Creating great Semantic SEO-friendly Content.\n\n\n[Tools]:\n- Perplexity_Sonar_Pro: Use this tool to Research the newest and accurate information that is needed in the Article methodology to create great semantic content that reflects the brand source context. (limit to 3 times)\n- Mcp_Checklist_Semantic_Content: Use this tool to retrieve the full requirements from the pre-loaded Google_HelpFul_Content and Checklist_Outline_and_Article_Methodology documents. **Call this tool without any input parameters.**\n- Think: Use the tool to think and ensure the final output will try its best to fulfill the search intent, the guidlines and the checklists from Mcp_checklist_semantic_content\n\n[Information about the current outline content]:\n{{\n  // Loại bỏ headers (##, ###), HR (---), bold (**), citations [n], link refs [n], và dọn dẹp bảng\n  $json['Outline Content hiện tại ']\n    .replace(/^[#]+\\s*/gm, '')         // Xóa ##, ### ở đầu dòng\n    .replace(/^---+\\s*$/gm, '')         // Xóa dòng ---\n    .replace(/\\*\\*/g, '')             // Xóa **\n    .replace(/\\[\\d+\\]/g, '')           // Xóa [1], [2]... (citations/link refs)\n    .replace(/^\\|-.*-\\|$/gm, '')       // Xóa dòng phân cách bảng |---|\n    .replace(/^\\|\\s*/gm, '')           // Xóa | ở đầu dòng (cho bảng)\n    .replace(/\\s*\\|$/gm, '')           // Xóa | ở cuối dòng (cho bảng)\n    .replace(/\\s*\\|\\s*/g, ' | ')       // Chuẩn hóa dấu | trong bảng\n    .trim()                           // Xóa khoảng trắng thừa đầu/cuối\n}}\n\n[Search Intent of the Keywords]:\n1. The Search Intent of the keyword is: {{ $('Process Form Data').item.json.formData['Mục đích tìm kiếm'] }}\n2. Also this is the detailed search intent:\n{{\n  // Tương tự, làm sạch phần phân tích search intent chi tiết\n  $('Store Search Intent').item.json['Search Intent analyze result']\n    .replace(/^[#]+\\s*/gm, '')\n    .replace(/^---+\\s*$/gm, '')\n    .replace(/\\*\\*/g, '')\n    .replace(/\\[\\d+\\]/g, '')\n    .replace(/^\\|-.*-\\|$/gm, '')\n    .replace(/^\\|\\s*/gm, '')\n    .replace(/\\s*\\|$/gm, '')\n    .replace(/\\s*\\|\\s*/g, ' | ')\n    .trim()\n}}\n\n[Source context of the brand]:\n1. Brand name: \"{{ $('Process Form Data').item.json.formData['Brand Name'] }}\".\n2. Brand information: \"{{ $('Process Form Data').item.json.formData['Brand information'].replace(/^- /gm, '') }}\" \n3. Brand solution: \"{{ $('Process Form Data').item.json.formData['Brand solution'].replace(/^- /gm, '') }}\" \n\n[Task/Instruction]:\nYour task is to Re-optimize, adjust or developed a detailed outline that based on the current outline and ensure the guidelines below. \n\nYou also can call the necessary tools based on the user request. \n\n[Guidlines for the outline]\nI/ Structure & Flow:\n\nThe outline will proceed from the \"Outline focus\" and \"title\" of the content to ensure the article's contextual flow, Contextual Vectors, Contextual hierarchy, and contextual Coverage. \n\n- The outline also needs to satisfy the user intent.\n\n- The outline should include one Heading 1 (H1) that serves as the central theme of the content.\n\nThe heading 2 (H2) sections will contain subtopics that directly support and elaborate on the main theme presented in the H1.\n\n- Heading 3 (H3) sections should further break down the H2 subtopics, ensuring a logical and smooth flow of information.\n\n- The content structure should be contextually coherent, with each section naturally leading to the next, maintaining a smooth and logical progression throughout the entire article.\n\nII/ Content Segmentation:\n\n- Main Content: Focus on delivering comprehensive coverage of the primary topic. This section should constitute more than 80% of the content, fully addressing the reader's queries and providing thorough explanations.\n\n- Supplemental Content: Enhance the main content with additional insights, perspectives, and supporting information. This should make up less than 20% of the content, offering extra value without diluting the main message.\n\n- Ensure a seamless contextual bridge between the main and supplemental content so the transition feels natural and cohesive.\n\nIII/ Optimization Criteria:\n\n- The first 10 headings (H2, H3, or even H4) should be high-quality and directly answer the reader's most pressing questions or concerns.- Group related or thematically connected headings together to maintain consistency and ease of navigation for the reader.\n\n- The supplemental content do not exceed 20% content of a whole article sometimes - should consist of questions, such as:\n-- Boolean Questions: Simple yes/no queries that clarify key points.\n-- Definitional Questions: Queries that explain or define essential terms or concepts.\n-- Grouping Questions: Questions that categorize or group related items together.\n-- Comparative Questions: Questions that compare different elements to provide deeper insights.\n\n- Do not include the article's conclusion in the content's outline.\n\nIV/ Contextual Harmony:- Maintain harmony in the hierarchical structure of headings (H2, H3, etc.) throughout the outline.\n\n- Ensure the first and last headings are interconnected through synonyms, antonyms, or related concepts to create a satisfying conclusion that ties back to the opening.- Use incremental lists where appropriate (e.g., when listing benefits or features) to enhance readability and organization.\n\nV/ Content Quality & Expertise:\n\n- The outline must demonstrate a high level of expertise and detail, ensuring the content is authoritative and credible and meets the \"user’s search intent\".\n\n- The overall outline should surpass existing content on the topic in terms of depth, quality, and user satisfaction, ensuring it ranks well on search engines like Google.\n\n[Guidelines for the article methodology]:\n\nFor each sections / heading, give me a corresponding article methodology (or detailed brief), that includes: \n- The content format: you will use bullet points, paragraphs, or tabular to write the content.\n- Estimate words: should be written\n- Main Ideas: what ideas/content should be included/addressed/proceed in the heading correspondingly to ensure the context coverage of the section accordingly, contextual vectors, contextual hierarchy & contextual flow of the whole content? (What content should be included in this corresponding heading/section?)]\n- What examples, data, or evidence to include\n- How this section connects to others\n\nYour outline should be comprehensive, strategic, and provide clear guidance for content creation that will outperform existing content on the same topic.\n\n[Examples output]:\n\nOkay, I understand. The \"Article Methodology\" description applies to the entire group of H3s following it, not just the first one. Similarly, the methodology for the tools applies to all listed tools.\n\nHere is the revised Markdown reflecting that structure:\n\n---\n\n# SEO Onpage là gì? Hướng dẫn 20+ tiêu chuẩn ưu Onpage 2025\n\nviết Introduction ở đây.\n\nTóm tắt về Onpage SEO tầm quan trọng ra sao? Và đưa cụ thể 3 case study từ đầu về việc tối ưu SEO Onpage giúp đạt kết quả cụ thể Organic Traffic gì? các keywords đại diện nào lên top nhưng không cần backlink (2 - 3 case, ví dụ: Sanf, Euro Travel, TOTO).\n\nnói tóm tắt về Onpage & Offpage khác nhau ra sao? Và tại sao tập trung master onpage lại quan trọng hơn là Offpage?\n\nNói về bài viết này sẽ đề cập:\n- Tiêu chí onpage basic nhưng hông phải ai cũng làm đúng & đủ.\n- Tiêu chí onpage nâng cao nào? Và việc ứng dụng tối ưu onpage nâng cao giúp đạt các lợi ích ra sao với đối thủ?\n  + cụ thể nói các tiêu chí onpage nâng cao mình nói là gì một cách giới thiệu ngắn gọn và giọng khẳng định rằng đa phần mọi người không biết.\n\nẢnh cụ thể về tổng hợp toàn bộ check list tối ưu SEO Onpage từ cơ bản đến nâng cao\n\nNgoài ra, chuyển tiếp nội dung về supplement content thông qua việc (các công cụ hỗ trợ tối ưu SEO nhanh chóng, lẫn các câu hỏi đặt ra lúc tối ưu SEO)?\n\n2. Ước tính: 150 - 200 từ.\n\n\n## SEO Onpage là gì?\n\n1.  Trả lời dứt khoát, không dài dòng.\n    - Định nghĩa nói về:\n        + là gì?\n        + bao gồm những công việc ra sao?\n2.  Nói về SEO Onpage nằm trong quy trình SEO ra sao? Thông thường trước bước nào và sau bước nào? Tại sao nó quan trọng về lợi ích?\n    - Ở lợi ích, chia sẻ các điểm lợi ích ở format content dạng Bullet Point\n        + Về lợi ích, đưa ra các dữ liệu chứng minh, cụ thể: Theo tài liệu google tại sao quan trọng, và thiết yếu?\n        + Ngoài các lợi ích giúp ranking, có thể đưa các lợi ích khác như: Phương pháp white hat, tiết kiệm chi phí (vì không cần backlink), hiệu quả thể hiện nhanh chóng (đưa rõ khoảng khung thời gian test ở dự án GTV thường từ 7 - 14 ngày để thấy kết quả, trong 30 ngày để fully effective).\n        + Expand evidence thông qua đưa số liệu case study GTV (ví dụ: > 85% Project GTV là không sử dụng backlink)\n3.  Đưa câu kết luận và chuyển tiếp cho việc phân biết Onpage & offpage là tối ưu những gì?\n4. Ước tính: 250 - 300 từ.\n### Phân biệt giữa SEO Onpage và SEO Offpage\n\n1.  Đưa câu trả lời dứt khoát một cách tóm tắt về Onpage & offpage khác nhau về định nghĩa ra sao.\n    - Sau đó kẻ bảng sự khác biệt về công việc, hiệu quả, nhiệm vụ của 2 hạng mục này.\n2.  Tóm tắt lợi ích tập trung onpage SEO sau đó dùng câu chuyển tiếp tới tiêu chí tối ưu Onpage cơ bản\n3. Ước tính: 120 - 180 từ.\n\n## 11+ tiêu chuẩn tối ưu SEO Onpage được Google ưu tiên (Kèm checklist)\n\nBổ sung hình ảnh checklist, tương tự:\nhttps://blog.hubspot.com/blog/tabid/6307/bid/33655/a-step-by-step-guide-to-flawless-on-page-seo-free-template.aspx\n\n**Với mỗi section về checklist trong danh sách dưới đây sẽ được viết bài theo Logic/phương pháp sau:**\n\n1.  Đưa câu trả lời dứt khoát về định nghĩa ở đây. (format paragaph)\n2.  Sau đó đưa lợi ích tối ưu (format paragaph)\n3.  Đưa tiêu chí tối ưu ở dạng bullet point và gỉai thích ngắn gọn tại sao phải tối ưu như vậy?\n4.  Bổ sung hình ảnh minh hoạ ở các tiêu chí tối ưu bên dưới được liệt kê.\n    - URL: mục URL ngắn & Dài.\n    - Title: Có số & không có số, giúp tăng CTR.\n    - Đáp ứng search intent: một hình vẽ được thiết kế, có text thể hiện ngắn gọn 9 loại intent.\n    - TOC: hình ảnh cho người dùng hình dung TOC là gì.\n    - Hình ảnh: So sánh giữa việc chèn từ khoá trong hình ảnh, và hình ảnh được mô tả rõ ràng (descriptive image).\n    - Readability: so sánh giữa hai bài có và không có tối ưu.\n6. Ước tính: 150 - 200 từ mỗi section.\n\n**Lưu ý:**\n1.  Luôn giới thiệu hình ảnh trước khi đưa. Tức có câu dẫn dắt.\n2.  Những gì mà hình ảnh đã thể hiện, bạn không cần nói lại trong text nội dung (ví dụ 9 loại search intent, phần này chỉ cần ghi là \"hiện tại có 9 loại search intent phổ biến trên google, được thể hiện ở hình ảnh dưới\" là xong, và bạn không cần nói 9 loại search intent gì).\n\nBạn chỉ nói lại trong dạng text nếu thật sự cần thiết.\n\n7.  Nếu có Tips, checklist tối ưu nâng cao trong hạng mục tương ứng. Ví dụ Title có checklist tối ưu CTR riêng chẳng hạn, thì bạn nên tạo một paragrah ngắn giới thiệu, sau đó nói checklist nâng cao ngắn gọn (vì cái này thuộc dạng bổ sung thêm, vì vậy checklist nâng cao chỉ cần nói tối đa 3 - 4 tiêu chí).\n8. ưu tiên việc Kẻ bảng thay vì trình bày nội dung bulletpoint sẽ tối ưu hơn: https://prnt.sc/V00f3Y03dll0\nTham khảo: https://www.semrush.com/blog/on-page-seo-checklist/#6--add-target-keywords-to-your-body-content\n\n### 1. URL\n\n### 2. Title\n\n### 3. Heading 1\n\n### 4. Heading 2-3\n\n### 5. Keyword Density\n\n### 6. Content unique, chuyên sâu và đáp ứng Search Intent\n\n### 7. Tối ưu Meta Description\n\n### 8. Hình ảnh\n\n### 9. Tối ưu Semantic - LSI Keyword\n\n### 10. In đậm keyword chính trong bài\n\n### 11. TOC (Table of Content – Mục lục)\n\n## 9+ Tiêu chuẩn Onpage nâng cao hiệu quả\n\n**Với mỗi section về checklist trong danh sách nâng cao dưới đây sẽ được viết bài theo Logic/phương pháp sau:**\n\n1.  Đưa câu trả lời dứt khoát về định nghĩa ở đây. (format paragaph)\n2.  Sau đó đưa lợi ích tối ưu (format paragaph)\n3.  Đưa tiêu chí tối ưu ở dạng bullet point và gỉai thích ngắn gọn tại sao phải tối ưu như vậy.\n4.  Ở các phần nâng cao, có những mục mình sẽ chèn video, những phần này khi chèn video thì cũng có ngữ cảnh dẫn dắt. Cũng như trong nội dung bài viết chỉ cần viết vắn tắt định nghĩa, lý do & vài checklist. Còn lại về hướng dẫn, minh hoạ mình sẽ kêu user coi video. Cụ thể:\n    - Feature snippet: https://www.youtube.com/watch?v=2F27882jH5I\n    - BlockQuotes: https://www.youtube.com/watch?v=zMz_R_ZYZzw\n    - Title nâng cao: https://www.youtube.com/watch?v=cWN9DXTt_bw\n    - Schema: https://www.youtube.com/watch?v=VNdvQMdSVdk\n5.  Bổ sung hình ảnh minh hoạ ở tương ứng tiêu chí tối ưu trong mục nâng cao này để User hiểu về định nghĩa hoặc hình dung là tối ưu ở đâu?\n6. Ước tính: 150 - 200 từ mỗi section.\n\n**Lưu ý:**\n1.  Luôn giới thiệu hình ảnh trước khi đưa. Tức có câu dẫn dắt.\n2.  Những gì mà hình ảnh đã thể hiện, bạn không cần nói lại trong text nội dung (ví dụ 9 loại search intent, phần này chỉ cần ghi là \"hiện tại có 9 loại search intent phổ biến trên google, được thể hiện ở hình ảnh dưới\" là xong, và bạn không cần nói 9 loại search intent gì).\n\nBạn chỉ nói lại trong dạng text nếu thật sự cần thiết.\n\n### 1. Featured Snippets\n\n### 2. Internal Link và Outbound Link\n\n### 3. Blockquote\n\n### 4. Tối ưu tiêu đề nâng cao\n\n### 5. Content GAP\n\n### 7. Schema Markup\n\n### 8. E-E- A- T\n\n## Các công cụ check SEO Onpage hiệu quả, phổ biến hiện nay?\n\nCông cụ Check SEO Onpage là ... Tuỳ vào mục tiêu và ngân sách ... mà tính hiệu quả là khác nhau. Check trong quá trình tối ưu SEO Onpage và Check sau khi tối ưu SEO Onapge. Sẽ có những công cụ ... hay ... Dưới đây là 6 công cụ Check SEO Onpage hiệu quả, sắp xếp theo tiêu chí: chi phí --> làm được gì.\n\n**Đối với mỗi công cụ được liệt kê dưới đây, cần trình bày theo cấu trúc:**\n\n1. *(Dùng Incremental list, tức các công cụ phổ biến dc dùng rộng rải ở Việt Nam. Vì là supplement content nên có thể chỉ cần đưa bulletpoint rồi giới thiệu không cần phải dùng heading 3.)*\n- là gì?\n- giúp gì?\n- chi phí bao nhiêu?\n2. Ước tính: 100 - 150 từ mỗi section.\n## 1. SEOQuake\n\n## 2. Yoast SEO / Rank math\n\n## 3. Surfer SEO\n\n## 4. Website Auditor\n\n## 5. Cora SEO\n\n---"}}, "id": "2845e040-cfbd-4c99-9d83-574fa541650f", "name": "Content Outline Generator", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [480, 368]}, {"parameters": {"assignments": {"assignments": [{"id": "e0a93d8d-4363-467e-9a13-e8e4df96dbb9", "name": "ContentOutline", "type": "string", "value": "={{ $json.output }}"}]}, "options": {}}, "id": "df78896c-7e51-4a1c-8a54-839ccd1f9467", "name": "Store Content Outline", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [864, 368]}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Create a slug for the following outline of the Blogpost/Content: \n[{{ $('Store Content Outline').item.json.ContentOutline }}]\n\nA slug in a blog post is the part of the URL that comes after the domain name and identifies a specific page. It is typically a short, descriptive phrase that summarizes the content of the post, making it easier for users and search engines to understand what the page is about. For example, in the URL www.example.com/intelligent-agents, the slug is intelligent-agents. A good slug is concise, contains relevant keywords, and avoids unnecessary words to improve readability and SEO. \n\nThe slug must be 4 or 5 words max and must include the primary keyword of the blog post which is {{ $('Process Form Data').item.json.formData.Query }}. Try it as short as possible.\n\nYour output must be the slug and nothing else so that I can copy and paste your output and put it at the end of my blog post URL to post it right away. "}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [192, 832], "id": "86817137-6ab3-46e4-8afe-d6f9fd347588", "name": "Slug"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-2024-11-20", "mode": "list", "cachedResultName": "GPT-4O-2024-11-20"}, "messages": {"values": [{"content": "=Extract the blog post title from the Blogpost/Content: \n[{{ $('Store Content Outline').item.json.ContentOutline }}]\n\n\nThe blog post title must include the primary keyword {{ $('Google Sheets1').item.json.Query }} and must inform the users right away of what they can expect from reading the blog post. \n\n- Don't put the output in \"\". The output should just text with no markdown or formatting. \n\nYour output must only be the blog post title and nothing else. "}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [560, 832], "id": "ada07f71-8d91-4bc0-8176-5b9d60525393", "name": "Title"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-2024-11-20", "mode": "list", "cachedResultName": "GPT-4O-2024-11-20"}, "messages": {"values": [{"content": "=Create a good meta description for the following blog post: \n\n[{{ $('Store Content Outline').item.json.ContentOutline }}]\n\nA good meta description for a blog post that is SEO-optimized should:\n- Be Concise: Stick to 150-160 characters to ensure the full description displays in search results. \n- Include Keywords: Incorporate semantic keywords that are relate to the main keyword which is [{{ $('Process Form Data').item.json.formData.Query }}] naturally to improve visibility and relevance to search queries \n- Provide Value: Clearly describe what the reader will learn or gain by clicking the link. \n\n- Be Engaging: Use persuasive language, such as action verbs or a question, to encourage clicks. \n\n- Align with Content: Ensure the description accurately reflects the blog post to meet user expectations and reduce bounce rates. \n\nYour output must only be the meta description and nothing else. \n"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [960, 832], "id": "c1f45426-4f78-464b-8357-ad52acca28a9", "name": "Meta description"}, {"parameters": {"rule": {"interval": [{"field": "hours"}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, 0], "id": "de354698-2def-465f-89ec-abbf0fd75386", "name": "Schedule Trigger1"}, {"parameters": {"documentId": {"__rl": true, "value": "1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM", "mode": "list", "cachedResultName": "Ekotek | Workflow Automation N8n - Outline & Keywords Update", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM/edit#gid=*********"}, "filtersUI": {"values": [{"lookupColumn": "Status", "lookupValue": "Run"}]}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [224, 0], "id": "a14ca7e1-a133-47b0-8e1a-9c72736e1b81", "name": "Google Sheets1", "alwaysOutputData": true, "credentials": {"googleSheetsOAuth2Api": {"id": "wQQPRkUeeGCPHnlK", "name": "Google Sheets account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "4fc7d4cb-b109-4202-b75e-57fe1715e33d", "name": "=Search Intent analyze result", "value": "={{ $json.choices[0].message.content }}", "type": "object"}]}, "options": {"ignoreConversionErrors": true}}, "id": "74ed4f53-d078-4668-b886-7a4a10486567", "name": "Store Search Intent", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [864, 0]}, {"parameters": {"method": "POST", "url": "https://api.perplexity.ai/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"sonar-pro\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"[Knowledge about User's Search Intent]:  {{ JSON.stringify($json['Search Intent analyze result']).slice(1, -1) }}\\n\\n[Instruction]: Answer in Markdown format with clear structure, bullet points, and short paragraphs.\\n\\n[Role]: You are a Semantic SEO Expert who is a master of the Topical Authority Concept from Koray. Your task is to analyze the competitor's content outline with the provided 'User's Search Intent' in mind.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"## Task:\\nIdentify the 10 best online articles that answer the query: *{{ $('Process Form Data').item.json.formData.Query }}* which mean {{ $('Process Form Data').item.json.formData['Query in English'] }}, and satisfy the search intent that you have been provided\\n\\n### Requirements:\\n- Explain WHY these articles are the best.\\n- EXCLUDE any video content.\\n- Search and compare both *{{$('Process Form Data').item.json.formData['Output language']}}* & English articles, but prioritize *{{$('Process Form Data').item.json.formData['Output language']}}*.\\n\\n### Output Language:\\n*{{$('Process Form Data').item.json.formData['Output language']}}*\"\n    }\n  ],\n  \"options\": {\n    \"temperature\": 1,\n    \"max_tokens\": 32000\n  }\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1104, 0], "id": "f5abf022-8c09-4e1f-86f8-96d52ab56130", "name": "Get top 10 Results & Analyze"}, {"parameters": {"toolDescription": "Use this tool to Research the newest and accurate information that is needed in the Article methodology to create great semantic content that reflects the brand source context", "method": "POST", "url": "https://api.perplexity.ai/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"sonar-pro\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a Semantic SEO Expert who is a master of the Topical Authority Concept from Koray. Your task is to analyze the competitor's content outline or a specific URL based on the provided query.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"{query}\"\n    }\n  ],\n  \"options\": {\n    \"temperature\": 0.8,\n    \"max_tokens\": 20000\n  }\n}", "placeholderDefinitions": {"values": [{"name": "query", "description": "Performs search using Perplexity based on the provided query, which can include text and URLs. ", "type": "string"}]}}, "id": "6c2793f8-7acc-4ad8-9b82-21f97fb5d445", "name": "Perplexity_Sonar_Pro", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [512, 624]}, {"parameters": {"sseEndpoint": "https://gtvseo.app.n8n.cloud/mcp/ad951871-27a9-495f-bc4d-3d4fab6465f9/sse", "options": {}}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1, "position": [640, 624], "id": "8c91188b-9586-49c8-a637-8e6836803638", "name": "Mcp_Checklist_Semantic_Content"}, {"parameters": {"assignments": {"assignments": [{"id": "40e327aa-3eaf-4cd7-b59e-8273b3c05355", "name": "<PERSON><PERSON><PERSON> quả phân tích đối thủ top 10", "value": "={{ $json.choices[0].message.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1312, 0], "id": "e982bbc9-f5aa-45db-a84d-bd9f7e56cec6", "name": "<PERSON>"}, {"parameters": {"method": "POST", "url": "https://api.perplexity.ai/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"sonar-pro\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"[Instruction]: Answer in Markdown format with clear structure, bullet points, and short paragraphs.\\n\\n[Role]: You are a Researcher & Semantic SEO Expert who is a master of the Topical Authority Concept from Koray. Your task is to analyze the Search Intent behind the query.\"\n    },\n    {\n      \"role\": \"user\",\n     \"content\": \"Analyze the query: [{{ $json.formData.Query }} which mean {{ $json.formData['Query in English'] }}]; what would they be looking for if they searched it? What is the search intent on Google and how to fullfil it?  *{{$('Process Form Data').item.json.formData['Output language']}}* & English articles, but prioritize *{{$('Process Form Data').item.json.formData['Output language']}}*.\\n\\n### Output Language:\\n*{{$('Process Form Data').item.json.formData['Output language']}}*\"\n    }\n  ],\n  \"options\": {\n    \"temperature\": 1,\n    \"max_tokens\": 10000\n  }\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [640, 0], "id": "d6d28b23-3a33-4015-ae3f-d18bbc360524", "name": "Search Intent analyze"}, {"parameters": {"method": "POST", "url": "https://api.perplexity.ai/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"sonar-pro\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"[Knowledge about User's Search Intent]:{{ $('Store Search Intent').item.json['Search Intent analyze result'].replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"').replace(/\\n/g, '\\\\n') }}\\n\\n[competior's outline information]:{{ JSON.stringify($json['Kết quả phân tích đối thủ top 10']).slice(1, -1) }} }}\\n\\n[Instruction]: Answer in Markdown format with clear structure, bullet points, and short paragraphs.\\n\\n[Role]: [Role]:\\nYou are a Semantic SEO Expert knowledgeable about topical authority, semantic content, and Creating great SEO-friendly Content that meets the \\\"Search Quality Evaluator Guideline\\\", \\\"Google helpful content\\\" & \\\"google product review guideline\\\".\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Start by reading these documents: \\n-http://static.googleusercontent.com/media/guidelines.raterhub.com/en//searchqualityevaluatorguidelines.pdf \\n\\n-https://developers.google.com/search/docs/fundamentals/creating-helpful-content?hl=en \\n\\nThen, analyze these Articles that rank at the top for the keywords [{{ $('Google Sheets1').item.json.Query.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"').replace(/\\n/g, '\\\\n') }}]: \\n\\n{{ $('Google Sheets1').item.json['Top 3 URL'].replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"').replace(/\\n/g, '\\\\n') }}\\n\\nBased strictly on the guidelines or principles outlined in the document and the “Search Quality Evaluator Guideline” PDF from Google, analyze **these articles and the top 10 articles** above. Then, compare them in terms of depth and details of the content, the demonstration of expertise and credibility, and how well they fulfill the user’s intent. \\n\\nI want you to create the outline for the content of the keywords I’ve provided. The outline has to be better than the competitor's or at least as good as theirs.\\n\\n\\n### [the output is in {{ $('Google Sheets1').item.json['Output language'].replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"').replace(/\\n/g, '\\\\n') }} and only have the Final outline]\"\n    }\n  ],\n  \"options\": {\n    \"temperature\": 1,\n    \"max_tokens\": 32000\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [32, 368], "id": "9d784e4a-b3f6-4453-84c5-1e0b11c32e6e", "name": "Create Outline of the content"}, {"parameters": {"assignments": {"assignments": [{"id": "40e327aa-3eaf-4cd7-b59e-8273b3c05355", "name": "Outline Content hiện tại ", "value": "={{ $json.choices[0].message.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [224, 368], "id": "fc6ba16f-29ea-4c89-ad63-dfce35144bbc", "name": "Edit Fields1"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [768, 624], "id": "773b3e73-809f-4dfa-b74b-64cb6a7cc9e6", "name": "Think"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "gpt-4.1"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [144, 640], "id": "d5804dbe-950c-4c8c-84d2-91b663da83bb", "name": "GPT 4.1"}, {"parameters": {"folderId": "15lMM-u_LW0qHn6jb5AEWZXLwtbCqyNgJ", "title": "={{ $('Google Sheets1').item.json.Query }} - Outline & Article methodology"}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [1104, 368], "id": "eeffe3bc-49d9-423d-b240-daa9c104906c", "name": "Google Docs"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "112dEFb28wk2Aj7kFWWYiT76ef0PmLpGQZKbwfrleIws", "mode": "list", "cachedResultName": "Workflow Automation N8n - Outline & AM  Update", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/112dEFb28wk2Aj7kFWWYiT76ef0PmLpGQZKbwfrleIws/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/112dEFb28wk2Aj7kFWWYiT76ef0PmLpGQZKbwfrleIws/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"row_number": "={{ $('Google Sheets1').item.json.row_number }}", "Status": "Processing", "Outline Result ": "={{ $('Store Content Outline').item.json.ContentOutline }}", "Link outline": "={{ $json.documentId }}"}, "matchingColumns": ["row_number"], "schema": [{"id": "Brand Name", "displayName": "Brand Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Brand information", "displayName": "Brand information", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Brand solution", "displayName": "Brand solution", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Query", "displayName": "Query", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Query in English", "displayName": "Query in English", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "outline focus", "displayName": "outline focus", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Top 3 URL", "displayName": "Top 3 URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Output language", "displayName": "Output language", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON> đ<PERSON>ch tìm ki<PERSON>m", "displayName": "<PERSON><PERSON><PERSON> đ<PERSON>ch tìm ki<PERSON>m", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Outline Result ", "displayName": "Outline Result ", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Link outline", "displayName": "Link outline", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON> c<PERSON>", "displayName": "<PERSON><PERSON><PERSON> c<PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [0, 832], "id": "d3ba9d41-d71a-4eee-b3ef-84634c7d0b21", "name": "Update l<PERSON>n Sheet", "credentials": {"googleSheetsOAuth2Api": {"id": "wQQPRkUeeGCPHnlK", "name": "Google Sheets account"}}}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "112dEFb28wk2Aj7kFWWYiT76ef0PmLpGQZKbwfrleIws", "mode": "list", "cachedResultName": "Workflow Automation N8n - Outline & AM  Update", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/112dEFb28wk2Aj7kFWWYiT76ef0PmLpGQZKbwfrleIws/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/112dEFb28wk2Aj7kFWWYiT76ef0PmLpGQZKbwfrleIws/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Meta Description": "={{ $json.message.content }}", "Title Content": "={{ $('Title').item.json.message.content }}", "Slug": "={{ $('Slug').item.json.message.content }}", "Status": "complete", "row_number": "={{ $('Google Sheets1').item.json.row_number }}"}, "matchingColumns": ["row_number"], "schema": [{"id": "Brand Name", "displayName": "Brand Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Brand information", "displayName": "Brand information", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Brand solution", "displayName": "Brand solution", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Query", "displayName": "Query", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Query in English", "displayName": "Query in English", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "outline focus", "displayName": "outline focus", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Top 3 URL", "displayName": "Top 3 URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Output language", "displayName": "Output language", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON> đ<PERSON>ch tìm ki<PERSON>m", "displayName": "<PERSON><PERSON><PERSON> đ<PERSON>ch tìm ki<PERSON>m", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Outline Result ", "displayName": "Outline Result ", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Link outline", "displayName": "Link outline", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Title Content", "displayName": "Title Content", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Slug", "displayName": "Slug", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Meta Description", "displayName": "Meta Description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [1328, 832], "id": "e757e8c3-1545-4565-adbe-d8cd9ffe4344", "name": "Update Status full", "credentials": {"googleSheetsOAuth2Api": {"id": "wQQPRkUeeGCPHnlK", "name": "Google Sheets account"}}}, {"parameters": {"operation": "update", "documentURL": "={{ $json.id }}", "actionsUi": {"actionFields": [{"action": "insert", "text": "={{ $('Store Content Outline').item.json.ContentOutline }}"}]}}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [1280, 368], "id": "ed255231-fe6b-49b0-8229-42e657922382", "name": "Update outline & AM lên G<PERSON>s"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "claude-3-7-sonnet-********", "cachedResultName": "Claude 3.7 Sonnet"}, "options": {"maxTokensToSample": 64000, "thinking": true, "thinkingBudget": 10000}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.3, "position": [384, 624], "id": "da8eb143-d683-4d2e-943c-f62aa8ef754e", "name": "Anthropic <PERSON>", "credentials": {"anthropicApi": {"id": "PuWOcQfXgG44xm84", "name": "n8n_claude"}}}], "connections": {"Process Form Data": {"main": [[{"node": "Search Intent analyze", "type": "main", "index": 0}]]}, "Content Outline Generator": {"main": [[{"node": "Store Content Outline", "type": "main", "index": 0}]]}, "Store Content Outline": {"main": [[{"node": "Google Docs", "type": "main", "index": 0}]]}, "Slug": {"main": [[{"node": "Title", "type": "main", "index": 0}]]}, "Title": {"main": [[{"node": "Meta description", "type": "main", "index": 0}]]}, "Meta description": {"main": [[{"node": "Update Status full", "type": "main", "index": 0}]]}, "Schedule Trigger1": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}]]}, "Google Sheets1": {"main": [[{"node": "Process Form Data", "type": "main", "index": 0}]]}, "Store Search Intent": {"main": [[{"node": "Get top 10 Results & Analyze", "type": "main", "index": 0}]]}, "Get top 10 Results & Analyze": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Perplexity_Sonar_Pro": {"ai_tool": [[{"node": "Content Outline Generator", "type": "ai_tool", "index": 0}]]}, "Mcp_Checklist_Semantic_Content": {"ai_tool": [[{"node": "Content Outline Generator", "type": "ai_tool", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Create Outline of the content", "type": "main", "index": 0}]]}, "Search Intent analyze": {"main": [[{"node": "Store Search Intent", "type": "main", "index": 0}]]}, "Create Outline of the content": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Content Outline Generator", "type": "main", "index": 0}]]}, "Think": {"ai_tool": [[{"node": "Content Outline Generator", "type": "ai_tool", "index": 0}]]}, "Google Docs": {"main": [[{"node": "Update outline & AM lên G<PERSON>s", "type": "main", "index": 0}]]}, "Update lên Sheet": {"main": [[{"node": "Slug", "type": "main", "index": 0}]]}, "Update outline & AM lên GG Docs": {"main": [[{"node": "Update l<PERSON>n Sheet", "type": "main", "index": 0}]]}, "Anthropic Chat Model": {"ai_languageModel": [[{"node": "Content Outline Generator", "type": "ai_languageModel", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "ac5d7c077b33a009e2c00b5e3cc656daf6c625eeb3a2dbea6944510bb25fc9ec"}}