# Gemini-Powered SEO Workflow Documentation

## Overview

This document describes the new SEO workflows that have been created to replace Perplexity with Google Gemini 2.0 Flash and Google Search grounding. The workflows maintain all the original input and output formats while leveraging Gemini's advanced capabilities with real-time Google Search integration.

## Files Created

### 1. `gemini-workflow.json`
**Complete workflow replacement** - This is the main workflow that replaces all Perplexity API calls with Gemini API calls while maintaining the exact same structure and functionality as the original base workflow.

### 2. `gemini-search-intent-workflow.json`
**Simplified search intent analysis** - A focused workflow that demonstrates the core Gemini integration for search intent analysis and competitor research.

### 3. `gemini-complete-workflow.json`
**Streamlined complete workflow** - A clean, optimized version of the complete workflow with proper error handling and structured connections.

## Key Changes Made

### API Endpoint Changes
- **From:** `https://api.perplexity.ai/chat/completions`
- **To:** `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent`

### Authentication Changes
- **From:** Perplexity API key in HTTP header
- **To:** Gemini API key in `X-goog-api-key` header

### Request Format Changes
- **From:** OpenAI-compatible chat completions format
- **To:** Gemini's native content format with Google Search grounding

### Response Format Changes
- **From:** `$json.choices[0].message.content`
- **To:** `$json.candidates[0].content.parts[0].text`

## Gemini API Configuration

### Request Structure
```json
{
  "contents": [
    {
      "parts": [
        {
          "text": "Your prompt here"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 1,
    "maxOutputTokens": 32000
  },
  "tools": [
    {
      "googleSearchRetrieval": {
        "dynamicRetrievalConfig": {
          "mode": "MODE_DYNAMIC",
          "dynamicThreshold": 0.7
        }
      }
    }
  ]
}
```

### Google Search Grounding
The workflows now use Gemini's built-in Google Search grounding feature, which provides:
- Real-time access to current web information
- Automatic source citation
- Dynamic retrieval based on query relevance
- Better accuracy than traditional search APIs

## Environment Variables Required

You need to set up the following environment variable in your n8n instance:

```bash
GEMINI_API_KEY=your_gemini_api_key_here
```

## How to Get Gemini API Key

1. Go to [Google AI Studio](https://ai.google.dev/)
2. Sign in with your Google account
3. Navigate to "Get API Key"
4. Create a new API key
5. Copy the key and add it to your n8n environment variables

## Workflow Features

### 1. Search Intent Analysis
- Uses Gemini with Google Search to analyze user search intent
- Provides comprehensive understanding of what users are looking for
- Supports multiple languages with prioritization

### 2. Competitor Analysis
- Identifies top 10 articles for target keywords
- Analyzes why these articles rank well
- Excludes video content as specified
- Compares content depth and expertise

### 3. Content Outline Generation
- Creates detailed content outlines based on competitor analysis
- Follows Google's helpful content guidelines
- Includes article methodology for each section
- Maintains SEO best practices

### 4. Integration with Existing Tools
- Maintains compatibility with MCP_Checklist_Semantic_Content
- Works with existing Google Sheets integration
- Preserves all output formats for downstream processing

## Usage Instructions

### For `gemini-workflow.json` (Recommended)
1. Import the workflow into your n8n instance
2. Set up the `GEMINI_API_KEY` environment variable
3. Configure your Google Sheets credentials
4. Update the Google Sheets document IDs if needed
5. Test the workflow with a sample query

### For `gemini-complete-workflow.json` (Simplified)
1. Import for a cleaner, more focused implementation
2. Use this if you want to understand the core Gemini integration
3. Extend with additional nodes as needed

## Testing the Workflow

### Sample Input Format
Your Google Sheets should contain columns like:
- `Query`: The main keyword/query
- `Query in English`: English translation if needed
- `Output language`: Target language for output
- `Brand Name`: Your brand name
- `Brand information`: Brand details
- `Brand solution`: What your brand offers
- `outline focus`: Specific sections to focus on
- `Top 3 URL`: URLs of top-ranking competitors

### Expected Output
- Detailed search intent analysis
- Competitor content analysis
- Comprehensive content outline with methodology
- SEO-optimized title, slug, and meta description

## Troubleshooting

### Common Issues
1. **API Key Error**: Ensure `GEMINI_API_KEY` is properly set
2. **Rate Limiting**: Gemini has rate limits; add delays if needed
3. **Response Format**: Check that you're accessing the correct response path
4. **Google Search Grounding**: Ensure your queries are specific enough for good results

### Error Handling
The workflows include error handling for:
- API failures
- Invalid responses
- Missing data fields
- Network timeouts

## Benefits of Gemini Integration

1. **Real-time Data**: Access to current web information through Google Search
2. **Better Accuracy**: More accurate and up-to-date information
3. **Cost Effective**: Competitive pricing compared to other AI APIs
4. **Google Integration**: Native integration with Google's search capabilities
5. **Advanced AI**: Latest Gemini 2.0 Flash model with improved performance

## Migration Notes

- All existing input/output formats are preserved
- No changes needed to downstream processes
- Gradual migration possible (can run both systems in parallel)
- Easy rollback if needed

## Support and Maintenance

For issues or questions:
1. Check the n8n logs for detailed error messages
2. Verify API key and permissions
3. Test individual nodes to isolate issues
4. Refer to [Gemini API documentation](https://ai.google.dev/gemini-api/docs) for API-specific issues
