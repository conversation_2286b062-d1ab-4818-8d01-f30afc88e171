{"meta": {"instanceId": "gemini-corrected-seo-workflow"}, "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours"}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, 0], "id": "de354698-2def-465f-89ec-abbf0fd75386", "name": "Schedule Trigger"}, {"parameters": {"documentId": {"__rl": true, "value": "1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM", "mode": "list", "cachedResultName": "Ekotek | Workflow Automation N8n - Outline & Keywords Update", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM/edit#gid=*********"}, "filtersUI": {"values": [{"lookupColumn": "Status", "lookupValue": "Run"}]}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [200, 0], "id": "a14ca7e1-a133-47b0-8e1a-9c72736e1b81", "name": "Google Sheets Input", "alwaysOutputData": true, "credentials": {"googleSheetsOAuth2Api": {"id": "wQQPRkUeeGCPHnlK", "name": "Google Sheets account"}}}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-goog-api-key", "value": "={{ $vars.GEMINI_API_KEY }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"[Instruction]: Answer in Markdown format with clear structure, bullet points, and short paragraphs.\\n\\n[Role]: You are a Researcher & Semantic SEO Expert who is a master of the Topical Authority Concept from Koray. Your task is to analyze the Search Intent behind the query.\\n\\nAnalyze the query: [{{ $json.Query }}] which means [{{ $json['Query in English'] }}]; what would they be looking for if they searched it? What is the search intent on Google and how to fulfill it? Search and compare both {{ $json['Output language'] }} & English articles, but prioritize {{ $json['Output language'] }}.\\n\\n### Output Language: {{ $json['Output language'] }}\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 1,\n    \"maxOutputTokens\": 10000\n  },\n  \"tools\": [\n    {\n      \"google_search\": {}\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, 0], "id": "d6d28b23-3a33-4015-ae3f-d18bbc360524", "name": "Gemini Search Intent Analysis"}, {"parameters": {"assignments": {"assignments": [{"id": "4fc7d4cb-b109-4202-b75e-57fe1715e33d", "name": "searchIntentResult", "value": "={{ $json.candidates[0].content.parts[0].text }}", "type": "string"}]}, "options": {"ignoreConversionErrors": true}}, "id": "74ed4f53-d078-4668-b886-7a4a10486567", "name": "Store Search Intent", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [600, 0]}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-goog-api-key", "value": "={{ $vars.GEMINI_API_KEY }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"[Knowledge about User's Search Intent]: {{ $json.searchIntentResult }}\\n\\n[Instruction]: Answer in Markdown format with clear structure, bullet points, and short paragraphs.\\n\\n[Role]: You are a Semantic SEO Expert who is a master of the Topical Authority Concept from Koray. Your task is to analyze the competitor's content outline with the provided 'User's Search Intent' in mind.\\n\\n## Task:\\nIdentify the 10 best online articles that answer the query: [{{ $('Google Sheets Input').item.json.Query }}] which means [{{ $('Google Sheets Input').item.json['Query in English'] }}], and satisfy the search intent that you have been provided\\n\\n### Requirements:\\n- Explain WHY these articles are the best.\\n- EXCLUDE any video content.\\n- Search and compare both {{ $('Google Sheets Input').item.json['Output language'] }} & English articles, but prioritize {{ $('Google Sheets Input').item.json['Output language'] }}.\\n\\n### Output Language: {{ $('Google Sheets Input').item.json['Output language'] }}\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 1,\n    \"maxOutputTokens\": 32000\n  },\n  \"tools\": [\n    {\n      \"google_search\": {}\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, 0], "id": "f5abf022-8c09-4e1f-86f8-96d52ab56130", "name": "Gemini Top 10 Analysis"}, {"parameters": {"assignments": {"assignments": [{"id": "40e327aa-3eaf-4cd7-b59e-8273b3c05355", "name": "top10AnalysisResult", "value": "={{ $json.candidates[0].content.parts[0].text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1000, 0], "id": "e982bbc9-f5aa-45db-a84d-bd9f7e56cec6", "name": "Store Top 10 Analysis"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-goog-api-key", "value": "={{ $vars.GEMINI_API_KEY }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"[Knowledge about User's Search Intent]: {{ $('Store Search Intent').item.json.searchIntentResult }}\\n\\n[Competitor Analysis]: {{ $json.top10AnalysisResult }}\\n\\n[Brand Context]:\\n- Brand: {{ $('Google Sheets Input').item.json['Brand Name'] }}\\n- Information: {{ $('Google Sheets Input').item.json['Brand information'] }}\\n- Solution: {{ $('Google Sheets Input').item.json['Brand solution'] }}\\n\\n[Instruction]: Create a comprehensive SEO content outline based on the search intent analysis and competitor research.\\n\\n[Role]: You are a Semantic SEO Expert who creates content outlines that follow Google's helpful content guidelines and E-E-A-T principles.\\n\\n## Task:\\nCreate a detailed content outline for the query: [{{ $('Google Sheets Input').item.json.Query }}]\\n\\n### Requirements:\\n- Include the required sections: {{ $('Google Sheets Input').item.json['outline focus'] }}\\n- Structure with H1, H2, H3 headings\\n- Include article methodology for each section\\n- Estimate word counts\\n- Specify content format (paragraphs, bullet points, tables)\\n- Better than competitor content\\n\\n### Output Language: {{ $('Google Sheets Input').item.json['Output language'] }}\\n\\nProvide the outline in markdown format ready for Google Docs.\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 0.8,\n    \"maxOutputTokens\": 32000\n  },\n  \"tools\": [\n    {\n      \"google_search\": {}\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1200, 0], "id": "9d784e4a-b3f6-4453-84c5-1e0b11c32e6e", "name": "Gemini Create Content Outline"}, {"parameters": {"assignments": {"assignments": [{"id": "40e327aa-3eaf-4cd7-b59e-8273b3c05355", "name": "contentOutline", "value": "={{ $json.candidates[0].content.parts[0].text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1400, 0], "id": "fc6ba16f-29ea-4c89-ad63-dfce35144bbc", "name": "Store Content Outline"}], "connections": {"Schedule Trigger": {"main": [[{"node": "Google Sheets Input", "type": "main", "index": 0}]]}, "Google Sheets Input": {"main": [[{"node": "Gemini Search Intent Analysis", "type": "main", "index": 0}]]}, "Gemini Search Intent Analysis": {"main": [[{"node": "Store Search Intent", "type": "main", "index": 0}]]}, "Store Search Intent": {"main": [[{"node": "Gemini Top 10 Analysis", "type": "main", "index": 0}]]}, "Gemini Top 10 Analysis": {"main": [[{"node": "Store Top 10 Analysis", "type": "main", "index": 0}]]}, "Store Top 10 Analysis": {"main": [[{"node": "Gemini Create Content Outline", "type": "main", "index": 0}]]}, "Gemini Create Content Outline": {"main": [[{"node": "Store Content Outline", "type": "main", "index": 0}]]}}}