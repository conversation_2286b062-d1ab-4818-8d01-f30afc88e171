{"meta": {"instanceId": "gemini-corrected-seo-workflow"}, "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours"}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, 0], "id": "de354698-2def-465f-89ec-abbf0fd75386", "name": "Schedule Trigger"}, {"parameters": {"documentId": {"__rl": true, "value": "1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM", "mode": "list", "cachedResultName": "Ekotek | Workflow Automation N8n - Outline & Keywords Update", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1CCRSGqJ8ZUlCbfyQzXBByJVf2uPiKRremaNFBur82lM/edit#gid=*********"}, "filtersUI": {"values": [{"lookupColumn": "Status", "lookupValue": "Run"}]}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [200, 0], "id": "a14ca7e1-a133-47b0-8e1a-9c72736e1b81", "name": "Google Sheets Input", "alwaysOutputData": true, "credentials": {"googleSheetsOAuth2Api": {"id": "wQQPRkUeeGCPHnlK", "name": "Google Sheets account"}}}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-goog-api-key", "value": "={{ $vars.GEMINI_API_KEY }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"[Instruction]: Answer in Markdown format with clear structure, bullet points, and short paragraphs.\\n\\n[Role]: You are a Senior SEO Strategist and Researcher who specializes in search intent analysis and competitive positioning for established SEO agencies. Your task is to analyze search intent through the lens of brand authority and market positioning.\\n\\n[Brand Context - {{ $json['Brand Name'] }}]:\\n{{ $json['Brand information'] }}\\n{{ $json['Brand solution'] }}\\n\\n[Query Analysis Task]:\\nAnalyze the search intent for: [{{ $json.Query }}] which means [{{ $json['Query in English'] }}]\\n\\n[Analysis Framework]:\\n\\n## 1. Search Intent Classification & User Journey\\n- Identify primary and secondary search intents\\n- Map user journey stage (awareness, consideration, decision)\\n- Analyze user expertise level and information needs\\n- Consider how {{ $json['Brand Name'] }}'s target audience (businesses needing SEO services and individuals seeking SEO training) would approach this query\\n\\n## 2. Brand Authority Positioning\\n- Analyze how {{ $json['Brand Name'] }}'s 10+ years experience and 500+ successful projects can be leveraged\\n- Identify opportunities to showcase expertise with notable clients (Vinamilk, Bamboo Airway, Pops WorldWide)\\n- Suggest ways to highlight the success of 5000+ SEO students and high-earning graduates\\n- Recommend authority signals that differentiate from generic SEO content\\n\\n## 3. Competitive Landscape Analysis\\n- Search and analyze both {{ $json['Output language'] }} & English content (prioritize {{ $json['Output language'] }})\\n- Identify content gaps where {{ $json['Brand Name'] }}'s expertise can provide superior value\\n- Analyze competitor positioning and find differentiation opportunities\\n- Assess how established agencies can outperform generic SEO content\\n\\n## 4. Content Strategy Recommendations\\n- Suggest content approaches that demonstrate thought leadership\\n- Recommend ways to integrate case studies and real project examples\\n- Propose methods to showcase both service expertise and training authority\\n- Identify opportunities for E-E-A-T (Experience, Expertise, Authoritativeness, Trustworthiness) optimization\\n\\n## 5. Brand-Specific Value Propositions\\n- Recommend how to position content to attract both service clients and training students\\n- Suggest ways to demonstrate ROI and measurable results\\n- Propose methods to showcase the practical, results-driven approach\\n- Identify opportunities to build trust through transparency and proven track record\\n\\n[Output Requirements]:\\n- Provide brand-aware insights, not generic analysis\\n- Include specific recommendations for {{ $json['Brand Name'] }}'s positioning\\n- Suggest concrete ways to demonstrate authority and expertise\\n- Focus on competitive differentiation through proven experience\\n- Consider both B2B service clients and individual training students as target audiences\\n\\n### Output Language: {{ $json['Output language'] }}\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 0.8,\n    \"maxOutputTokens\": 12000\n  },\n  \"tools\": [\n    {\n      \"google_search\": {}\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, 0], "id": "d6d28b23-3a33-4015-ae3f-d18bbc360524", "name": "Gemini Search Intent Analysis"}, {"parameters": {"assignments": {"assignments": [{"id": "4fc7d4cb-b109-4202-b75e-57fe1715e33d", "name": "searchIntentResult", "value": "={{ $json.candidates[0].content.parts[0].text }}", "type": "string"}]}, "options": {"ignoreConversionErrors": true}}, "id": "74ed4f53-d078-4668-b886-7a4a10486567", "name": "Store Search Intent", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [600, 0]}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-goog-api-key", "value": "={{ $vars.GEMINI_API_KEY }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"[Search Intent Analysis]: {{ $json.searchIntentResult }}\\n\\n[Brand Context - {{ $('Google Sheets Input').item.json['Brand Name'] }}]:\\n{{ $('Google Sheets Input').item.json['Brand information'] }}\\n{{ $('Google Sheets Input').item.json['Brand solution'] }}\\n\\n[Instruction]: Answer in Markdown format with clear structure, bullet points, and short paragraphs.\\n\\n[Role]: You are a Senior SEO Competitive Intelligence Analyst specializing in content gap analysis for established SEO agencies. Your expertise lies in identifying opportunities where proven industry leaders can outperform existing content.\\n\\n## Primary Task:\\nIdentify and analyze the 10 best-performing articles for the query: [{{ $('Google Sheets Input').item.json.Query }}] which means [{{ $('Google Sheets Input').item.json['Query in English'] }}]\\n\\n## Analysis Framework:\\n\\n### 1. Content Performance Analysis\\n- Identify the top 10 articles currently ranking for this query\\n- Analyze WHY these articles perform well (content depth, authority signals, user engagement factors)\\n- Evaluate their content structure, comprehensiveness, and user value\\n- EXCLUDE video content from analysis\\n\\n### 2. Authority & Credibility Assessment\\n- Assess the domain authority and brand credibility of ranking sites\\n- Identify content created by established agencies vs. generic content sites\\n- Analyze how these sites demonstrate expertise and trustworthiness\\n- Compare their authority signals against {{ $('Google Sheets Input').item.json['Brand Name'] }}'s credentials\\n\\n### 3. Content Gap Identification\\n- Identify specific gaps where {{ $('Google Sheets Input').item.json['Brand Name'] }}'s 10+ years experience can provide superior insights\\n- Find opportunities to showcase real project results from 500+ successful campaigns\\n- Identify areas where case studies from notable clients (Vinamilk, Bamboo Airway, Pops WorldWide) would add unique value\\n- Spot opportunities to demonstrate training expertise through student success stories\\n\\n### 4. Competitive Differentiation Opportunities\\n- Analyze how current content lacks practical, results-driven insights\\n- Identify opportunities for {{ $('Google Sheets Input').item.json['Brand Name'] }} to demonstrate superior expertise\\n- Find gaps in content that could be filled with real-world examples and proven methodologies\\n- Assess opportunities to showcase both service delivery and training authority\\n\\n### 5. Strategic Positioning Recommendations\\n- Recommend how {{ $('Google Sheets Input').item.json['Brand Name'] }} can position content to outrank existing articles\\n- Suggest authority-building elements that competitors lack\\n- Identify opportunities to target both B2B service clients and individual training students\\n- Recommend ways to demonstrate measurable ROI and proven track record\\n\\n## Search Requirements:\\n- Search and compare both {{ $('Google Sheets Input').item.json['Output language'] }} & English articles\\n- Prioritize {{ $('Google Sheets Input').item.json['Output language'] }} content in analysis\\n- Focus on articles from established SEO agencies, educational institutions, and authoritative marketing sites\\n- Analyze content depth, practical value, and authority signals\\n\\n## Output Focus:\\n- Provide actionable competitive intelligence for {{ $('Google Sheets Input').item.json['Brand Name'] }}\\n- Identify specific opportunities to leverage brand authority and experience\\n- Suggest concrete ways to differentiate from existing content\\n- Focus on how an established agency can outperform generic SEO content\\n\\n### Output Language: {{ $('Google Sheets Input').item.json['Output language'] }}\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 0.8,\n    \"maxOutputTokens\": 32000\n  },\n  \"tools\": [\n    {\n      \"google_search\": {}\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, 0], "id": "f5abf022-8c09-4e1f-86f8-96d52ab56130", "name": "Gemini Top 10 Analysis"}, {"parameters": {"assignments": {"assignments": [{"id": "40e327aa-3eaf-4cd7-b59e-8273b3c05355", "name": "top10AnalysisResult", "value": "={{ $json.candidates[0].content.parts[0].text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1000, 0], "id": "e982bbc9-f5aa-45db-a84d-bd9f7e56cec6", "name": "Store Top 10 Analysis"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-goog-api-key", "value": "={{ $vars.GEMINI_API_KEY }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"[Search Intent Analysis]: {{ $('Store Search Intent').item.json.searchIntentResult }}\\n\\n[Competitive Intelligence]: {{ $json.top10AnalysisResult }}\\n\\n[Brand Authority Context - {{ $('Google Sheets Input').item.json['Brand Name'] }}]:\\n{{ $('Google Sheets Input').item.json['Brand information'] }}\\n{{ $('Google Sheets Input').item.json['Brand solution'] }}\\n\\n[Content Requirements]:\\n- Focus Areas: {{ $('Google Sheets Input').item.json['outline focus'] }}\\n- Target Audience: Both B2B service clients and individual SEO training students\\n- Brand Positioning: Established industry leader with proven track record\\n\\n[Instruction]: Create a comprehensive, authority-driven SEO content outline that positions {{ $('Google Sheets Input').item.json['Brand Name'] }} as the definitive expert source.\\n\\n[Role]: You are a Senior Content Strategist for established SEO agencies, specializing in creating content that demonstrates industry leadership, builds trust through proven results, and converts both service prospects and training students.\\n\\n## Content Outline Creation Framework:\\n\\n### 1. Authority-First Structure\\n- Design content hierarchy that immediately establishes {{ $('Google Sheets Input').item.json['Brand Name'] }}'s credibility\\n- Integrate proof points (10+ years, 500+ projects, notable clients) naturally throughout\\n- Structure sections to build from foundational expertise to advanced insights\\n- Include strategic placement of authority signals and social proof\\n\\n### 2. Dual-Audience Optimization\\n- Balance content for B2B decision-makers seeking SEO services\\n- Include elements that attract individuals interested in SEO training\\n- Provide both strategic insights for businesses and actionable learning for practitioners\\n- Structure content to serve both immediate answers and deeper education\\n\\n### 3. Competitive Differentiation Strategy\\n- Leverage insights from competitive analysis to outperform existing content\\n- Include unique value propositions that only an established agency can provide\\n- Integrate real-world examples and case study opportunities\\n- Position content to demonstrate superior expertise and proven methodologies\\n\\n### 4. E-E-A-T Optimization Framework\\n- **Experience**: Integrate opportunities to showcase 10+ years of hands-on SEO work\\n- **Expertise**: Demonstrate deep technical knowledge and industry insights\\n- **Authoritativeness**: Position {{ $('Google Sheets Input').item.json['Brand Name'] }} as thought leader\\n- **Trustworthiness**: Include transparency, proven results, and client success stories\\n\\n### 5. Content Methodology Specifications\\n\\nFor each section, provide:\\n- **Content Format**: Specify paragraphs, bullet points, tables, case study boxes, etc.\\n- **Word Count Estimates**: Realistic estimates for comprehensive coverage\\n- **Authority Integration**: How to weave in {{ $('Google Sheets Input').item.json['Brand Name'] }}'s credentials naturally\\n- **Proof Point Opportunities**: Where to include client examples, student successes, or project results\\n- **Engagement Elements**: Interactive components, downloadables, or calls-to-action\\n- **SEO Optimization**: Keyword integration and search intent fulfillment\\n\\n### 6. Brand-Specific Content Enhancements\\n- Suggest opportunities to showcase Vinamilk, Bamboo Airway, Pops WorldWide case studies\\n- Recommend ways to highlight student success stories (high-earning graduates)\\n- Include strategic mentions of the 5000+ student community\\n- Propose methods to demonstrate measurable ROI and traffic growth\\n\\n### 7. Conversion Strategy Integration\\n- Design content flow to naturally lead to service inquiries\\n- Include strategic placement of training program mentions\\n- Balance educational value with business development opportunities\\n- Create natural transition points for deeper engagement\\n\\n## Output Requirements:\\n\\n### Content Structure:\\n- H1, H2, H3 heading hierarchy optimized for both users and search engines\\n- Logical flow that builds authority while serving search intent\\n- Strategic placement of brand authority signals throughout\\n- Clear article methodology for each major section\\n\\n### Brand Integration Guidelines:\\n- Naturally integrate {{ $('Google Sheets Input').item.json['Brand Name'] }}'s experience and credentials\\n- Include specific opportunities for case study integration\\n- Suggest strategic placement of social proof and authority signals\\n- Balance brand promotion with genuine educational value\\n\\n### Competitive Advantage Focus:\\n- Ensure content outline surpasses identified competitor weaknesses\\n- Leverage unique positioning as both service provider and training authority\\n- Include elements that only an established agency with proven track record can provide\\n- Position content to rank higher through superior depth and authority\\n\\n### Format Requirements:\\n- Provide complete outline in markdown format ready for Google Docs\\n- Include detailed methodology notes for content creators\\n- Specify exact content requirements for each section\\n- Include strategic notes for brand integration and authority building\\n\\n### Output Language: {{ $('Google Sheets Input').item.json['Output language'] }}\\n\\n**Goal**: Create an outline that positions {{ $('Google Sheets Input').item.json['Brand Name'] }} as the authoritative source while delivering exceptional user value and outperforming all competitor content.\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 0.7,\n    \"maxOutputTokens\": 35000\n  },\n  \"tools\": [\n    {\n      \"google_search\": {}\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1200, 0], "id": "9d784e4a-b3f6-4453-84c5-1e0b11c32e6e", "name": "Gemini Create Content Outline"}, {"parameters": {"assignments": {"assignments": [{"id": "40e327aa-3eaf-4cd7-b59e-8273b3c05355", "name": "contentOutline", "value": "={{ $json.candidates[0].content.parts[0].text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1400, 0], "id": "fc6ba16f-29ea-4c89-ad63-dfce35144bbc", "name": "Store Content Outline"}], "connections": {"Schedule Trigger": {"main": [[{"node": "Google Sheets Input", "type": "main", "index": 0}]]}, "Google Sheets Input": {"main": [[{"node": "Gemini Search Intent Analysis", "type": "main", "index": 0}]]}, "Gemini Search Intent Analysis": {"main": [[{"node": "Store Search Intent", "type": "main", "index": 0}]]}, "Store Search Intent": {"main": [[{"node": "Gemini Top 10 Analysis", "type": "main", "index": 0}]]}, "Gemini Top 10 Analysis": {"main": [[{"node": "Store Top 10 Analysis", "type": "main", "index": 0}]]}, "Store Top 10 Analysis": {"main": [[{"node": "Gemini Create Content Outline", "type": "main", "index": 0}]]}, "Gemini Create Content Outline": {"main": [[{"node": "Store Content Outline", "type": "main", "index": 0}]]}}}